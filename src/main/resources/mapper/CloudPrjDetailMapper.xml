<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
    namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.cloud.CloudPrjDetailMapper">
    <resultMap id="BaseResultMap"
               type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.cloud.CloudPrjDetailPO">
        <!--@mbg.generated-->
        <!--@Table rv_clound_project_detail-->
        <id column="id" jdbcType="CHAR" property="id"/>
        <result column="org_id" jdbcType="CHAR" property="orgId"/>
        <result column="clound_project_id" jdbcType="CHAR" property="cloundProjectId"/>
        <result column="source_id" jdbcType="CHAR" property="sourceId"/>
        <result column="dimension" jdbcType="TINYINT" property="dimension"/>
        <result column="create_user_id" jdbcType="CHAR" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="CHAR" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id
             , org_id
             , clound_project_id
             , source_id
             , dimension
             , create_user_id
             , create_time
             , update_user_id
             , update_time
    </sql>
    <insert id="batchInsertOrUpdate">
        <!--@mbg.generated-->
        insert into rv_clound_project_detail
            (id,
             org_id,
             clound_project_id,
             source_id,
             dimension,
             create_user_id,
             create_time,
             update_user_id,
             update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=CHAR},
             #{item.orgId,jdbcType=CHAR},
             #{item.cloundProjectId,jdbcType=CHAR},
             #{item.sourceId,jdbcType=CHAR},
             #{item.dimension,jdbcType=TINYINT},
             #{item.createUserId,jdbcType=CHAR},
             #{item.createTime,jdbcType=TIMESTAMP},
             #{item.updateUserId,jdbcType=CHAR},
             #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
        on duplicate key update
        <trim suffixOverrides=",">
            org_id            = values(org_id),
            clound_project_id = values(clound_project_id),
            source_id         = values(source_id),
            dimension         = values(dimension),
            update_user_id    = values(update_user_id),
            update_time       = values(update_time)
        </trim>
    </insert>

    <select id="selectByOrgIdAndPrjId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_clound_project_detail
        where org_id = #{orgId,jdbcType=CHAR}
          and clound_project_id = #{prjId,jdbcType=CHAR}
    </select>
</mapper>
