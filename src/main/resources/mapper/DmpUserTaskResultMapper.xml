<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper
    namespace="com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.user.DmpUserTaskResultMapper">
    <resultMap id="BaseResultMap"
               type="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.user.DmpUserTaskResultPO">
        <!--@mbg.generated-->
        <!--@Table rv_dmp_user_task_result-->
        <id column="id" jdbcType="CHAR" property="id"/>
        <result column="org_id" jdbcType="CHAR" property="orgId"/>
        <result column="dmp_id" jdbcType="CHAR" property="dmpId"/>
        <result column="task_id" jdbcType="CHAR" property="taskId"/>
        <result column="user_id" jdbcType="CHAR" property="userId"/>
        <result column="done" jdbcType="TINYINT" property="done"/>
        <result column="create_user_id" jdbcType="CHAR" property="createUserId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_user_id" jdbcType="CHAR" property="updateUserId"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id
             , org_id
             , dmp_id
             , task_id
             , user_id
             , done
             , create_user_id
             , create_time
             , update_user_id
             , update_time
    </sql>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into rv_dmp_user_task_result
            (id,
             org_id,
             dmp_id,
             task_id,
             user_id,
             done,
             create_user_id,
             create_time,
             update_user_id,
             update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id,jdbcType=CHAR},
             #{item.orgId,jdbcType=CHAR},
             #{item.dmpId,jdbcType=CHAR},
             #{item.taskId,jdbcType=CHAR},
             #{item.userId,jdbcType=CHAR},
             #{item.done,jdbcType=TINYINT},
             #{item.createUserId,jdbcType=CHAR},
             #{item.createTime,jdbcType=TIMESTAMP},
             #{item.updateUserId,jdbcType=CHAR},
             #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <insert id="batchInsertOrUpdate"
            parameterType="com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.user.DmpUserTaskResultPO">
        <!--@mbg.generated-->
        insert into rv_dmp_user_task_result
            (id,
             org_id,
             dmp_id,
             task_id,
             user_id,
             done,
             create_user_id,
             create_time,
             update_user_id,
             update_time)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id},
             #{item.orgId},
             #{item.dmpId},
             #{item.taskId},
             #{item.userId},
             #{item.done},
             #{item.createUserId},
             #{item.createTime},
             #{item.updateUserId},
             #{item.updateTime})
        </foreach>
        on duplicate key update
        <trim suffixOverrides=",">
            org_id         = values(org_id),
            dmp_id         = values(dmp_id),
            task_id        = values(task_id),
            user_id        = values(user_id),
            done           = values(done),
            update_user_id = values(update_user_id),
            update_time    = values(update_time)
        </trim>
    </insert>
    <select id="selectByDmpIdAndUserIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_dmp_user_task_result
        where org_id = #{orgId}
          and dmp_id = #{dmpId}
        <choose>
            <when test="userIds != null and userIds.size() != 0">
                and user_id in
                <foreach collection="userIds" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </select>

    <delete id="deleteByTaskIdAndUserIds">
        delete
        from rv_dmp_user_task_result where org_id = #{orgId}
                                       and task_id = #{taskId}
        <choose>
            <when test="userIds != null and userIds.size() != 0">
                and user_id in
                <foreach collection="userIds" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </delete>

    <delete id="deleteBatch">
        delete from rv_dmp_user_task_result
        where org_id = #{orgId}
        <choose>
            <when test="ids != null and ids.size() != 0">
                and id in
                <foreach collection="ids" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </delete>

    <delete id="deleteByTaskId">
        delete from rv_dmp_user_task_result
        where org_id = #{orgId} and task_id = #{taskId} and dmp_id = #{dmpId}
    </delete>

    <delete id="deleteByUserIds">
        delete from rv_dmp_user_task_result
        where org_id = #{orgId} and dmp_id = #{dmpId}
        <choose>
            <when test="delUserIds != null and delUserIds.size() != 0">
                and user_id in
                <foreach collection="delUserIds" item="item" index="index" open="(" separator=","
                         close=")">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                <!--@ignoreSql-->
                and 1 != 1
            </otherwise>
        </choose>
    </delete>

    <delete id="deleteByDmpId">
        delete from rv_dmp_user_task_result
        where org_id = #{orgId} and dmp_id = #{dmpId}
    </delete>

    <select id="selectByOrgId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from rv_dmp_user_task_result a
        where a.org_id = #{orgId}
        <!--只查询dmp和dmoTask未删除的记录-->
        and exists
        (
            select 1
            from rv_dmp      o
            join rv_dmp_task p on o.id = p.dmp_id
            where o.deleted = 0
              and p.deleted = 0
              and o.id = a.dmp_id
              and p.id = a.task_id
        )
    </select>
</mapper>
