-- liquibase formatted sql

-- changeset liquibase:1733295843967-1
CREATE TABLE rv_ai_tool_message (id BIGINT UNSIGNED NOT NULL COMMENT '雪花id', org_id CHAR(36) NOT NULL COMMENT '机构id', user_id CHAR(36) NOT NULL COMMENT '用户id', user_name VARCHAR(36) NOT NULL COMMENT '用户账号', fullname VARCHAR(128) NOT NULL COMMENT '用户姓名', qa_type TINYINT(3) NULL COMMENT '问题类型', orig_qa_text VARCHAR(2000) DEFAULT '' NOT NULL COMMENT '原始问题', qa_text VARCHAR(2000) DEFAULT '' NOT NULL COMMENT '修改后问题', answer_text MEDIUMTEXT NULL COMMENT '回答内容', session_id VARCHAR(36) DEFAULT '' NOT NULL COMMENT '用户会话Id', checked TINYINT(3) DEFAULT 0 NOT NULL COMMENT '是否确认过，默认：0', create_time datetime DEFAULT NOW(3) NOT NULL COMMENT '创建时间', update_time datetime DEFAULT NOW(3) NOT NULL COMMENT '更新时间', deleted TINYINT(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '是否删除.0：未删除，1：已删除', db_create_time datetime DEFAULT NOW(3) NOT NULL COMMENT '数据创建时间（数据库专用，禁止用于业务）', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NOT NULL COMMENT '数据修改时间（数据库专用，禁止用于业务）', useful TINYINT(3) NULL COMMENT 'ai给的答案是否有用', curr_node_instance_id VARCHAR(254) NULL COMMENT '生成本次对话的aibox节点', CONSTRAINT PK_RV_AI_TOOL_MESSAGE PRIMARY KEY (id)) COMMENT='自用AI问答工具-会话记录';

-- changeset liquibase:1733295843967-2
CREATE TABLE rv_ai_tool_session (id BIGINT UNSIGNED NOT NULL COMMENT '雪花id', org_id CHAR(36) NOT NULL COMMENT '机构id', user_id CHAR(36) NOT NULL COMMENT '用户id', session_id VARCHAR(36) NOT NULL COMMENT '会话ID', session_title VARCHAR(128) NULL COMMENT '会话标题', start_time datetime DEFAULT NOW(3) NOT NULL COMMENT '会话开始时间', end_time datetime NULL COMMENT '会话结束时间', active_status TINYINT(3) DEFAULT 1 NOT NULL COMMENT '会话活跃状态，1表示活跃，0表示结束', useful TINYINT(3) NULL COMMENT '本地会话是否对用户有用', create_time datetime DEFAULT NOW(3) NOT NULL COMMENT '创建时间', update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NOT NULL COMMENT '更新时间', deleted TINYINT(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '是否删除.0：未删除，1：已删除', db_create_time datetime DEFAULT NOW(3) NOT NULL COMMENT '数据创建时间（数据库专用，禁止用于业务）', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NOT NULL COMMENT '数据修改时间（数据库专用，禁止用于业务）', CONSTRAINT PK_RV_AI_TOOL_SESSION PRIMARY KEY (id)) COMMENT='自用AI问答工具-会话记录表';

-- changeset liquibase:1733295843967-3
CREATE TABLE rv_appendix (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', app_name VARCHAR(100) NOT NULL COMMENT '附件名称', app_url VARCHAR(255) NOT NULL COMMENT '附件链接', app_source TINYINT(3) DEFAULT 0 NOT NULL COMMENT '附件来源（1-校准会）', app_source_id CHAR(36) NULL COMMENT '附件来源主键Id', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间(数据库专用，禁止用于业务)', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间(数据库专用，禁止用于业务)', CONSTRAINT PK_RV_APPENDIX PRIMARY KEY (id)) COMMENT='附件表';

-- changeset liquibase:1733295843967-4
CREATE TABLE rv_calibration_meeting (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', project_id CHAR(36) NOT NULL COMMENT '盘点项目id', meet_status TINYINT(3) DEFAULT 0 NOT NULL COMMENT '校准会状态（0-未开始，1-进行中，2-已结束）', meet_name VARCHAR(50) NOT NULL COMMENT '会议名称', meet_time datetime NULL COMMENT '会议时间', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', meet_minutes VARCHAR(500) DEFAULT '' NOT NULL COMMENT '会议记录', deleted TINYINT(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '是否删除(0-否,1-是)', meet_route_status TINYINT(3) DEFAULT 1 NOT NULL COMMENT '显示校准路径 0 关闭 1开启', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间(数据库专用，禁止用于业务)', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间(数据库专用，禁止用于业务)', duration INT UNSIGNED DEFAULT 0 NOT NULL COMMENT '会议持续时间，单位分钟（用于绚星工作场会议）', meet_no VARCHAR(36) NULL COMMENT '会议号（11位号码）', meet_id VARCHAR(36) NULL COMMENT '会议id', CONSTRAINT PK_RV_CALIBRATION_MEETING PRIMARY KEY (id)) COMMENT='校准会';

-- changeset liquibase:1733295843967-5
CREATE TABLE rv_calibration_meeting_result (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', project_id CHAR(36) NOT NULL COMMENT '盘点项目id', meeting_id CHAR(36) NULL COMMENT '会议id', user_id CHAR(36) NOT NULL COMMENT '人员id', dimension_id CHAR(36) NOT NULL COMMENT '维度id', init_level TINYINT(3) DEFAULT 0 NOT NULL COMMENT '初始结果（0-默认，1-低，2-中，3-高）;导入或计算的初始结果', init_score DECIMAL(6, 2) DEFAULT -1 NOT NULL COMMENT '初始分数;导入或计算的初始结果', last_level TINYINT(3) DEFAULT 0 NOT NULL COMMENT '最新结果（0-默认，1-低，2-中，3-高）;校准过1次或n次的结果', last_score DECIMAL(6, 2) DEFAULT -1 NOT NULL COMMENT '最新分数;校准过1次或n次的结果', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', init_type VARCHAR(100) DEFAULT '0' NOT NULL COMMENT '数据来源 0：初始化 1：校准', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间(数据库专用，禁止用于业务)', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间(数据库专用，禁止用于业务)', CONSTRAINT PK_RV_CALIBRATION_MEETING_RESULT PRIMARY KEY (id)) COMMENT='盘点校准会结果表';

-- changeset liquibase:1733295843967-6
CREATE TABLE rv_calibration_meeting_user (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', meeting_id CHAR(36) NOT NULL COMMENT '会议id', user_id CHAR(36) NOT NULL COMMENT '干系人id', user_type TINYINT(3) DEFAULT 0 NOT NULL COMMENT '干系人类型（1-组织者，2-参会人）', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间(数据库专用，禁止用于业务)', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间(数据库专用，禁止用于业务)', CONSTRAINT PK_RV_CALIBRATION_MEETING_USER PRIMARY KEY (id)) COMMENT='校准会干系人员表';

-- changeset liquibase:1733295843967-7
CREATE TABLE rv_career_history (id VARCHAR(100) NOT NULL COMMENT '主键Id', third_user_id VARCHAR(100) NOT NULL COMMENT '三方用户id', org_id VARCHAR(100) NOT NULL COMMENT '机构Id', third_dept_name VARCHAR(255) DEFAULT '' NULL COMMENT '部门名称', third_position_name VARCHAR(255) DEFAULT '' NULL COMMENT '岗位名称', third_jobgrade_name VARCHAR(255) DEFAULT '' NULL COMMENT '职级名称', action_name VARCHAR(255) DEFAULT '' NOT NULL COMMENT '任职动作名称（入职、转正、晋升、转岗等）', occurrence_time datetime NOT NULL COMMENT '任职动作发生时间', deleted TINYINT(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '是否删除(0-否,1-是)', create_time datetime DEFAULT NOW(3) NULL COMMENT '创建时间', update_time datetime DEFAULT NOW(3) NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NOT NULL COMMENT '数据创建时间（数据库专用，禁止用于业务）', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NOT NULL COMMENT '数据修改时间（数据库专用，禁止用于业务）', third_career_history_id VARCHAR(100) NULL COMMENT '三方任职履历id', CONSTRAINT PK_RV_CAREER_HISTORY PRIMARY KEY (id)) COMMENT='任职履历表';

-- changeset liquibase:1733295843967-8
CREATE TABLE rv_category (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', category_name VARCHAR(50) NOT NULL COMMENT '分类名称', category_type TINYINT(3) DEFAULT 0 NOT NULL COMMENT '分类（0-盘点项目分类）', category_enable TINYINT(3) DEFAULT 0 NOT NULL COMMENT '是否启用（0-禁用，1-启用）', remark VARCHAR(400) DEFAULT '' NULL COMMENT '说明', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间(数据库专用，禁止用于业务)', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间(数据库专用，禁止用于业务)', CONSTRAINT PK_RV_CATEGORY PRIMARY KEY (id)) COMMENT='目录类别';

-- changeset liquibase:1733295843967-9
CREATE TABLE rv_clound_project (id CHAR(36) NOT NULL COMMENT 'id', org_id CHAR(36) NOT NULL COMMENT '机构id', clound_id CHAR(36) NOT NULL COMMENT '云方案管理表id', project_id CHAR(36) NOT NULL COMMENT '人才盘点项目id', project_name VARCHAR(200) NOT NULL COMMENT '方案名称', user_level VARCHAR(200) NOT NULL COMMENT '人群层级', industrty_type TINYINT(3) UNSIGNED DEFAULT 0 NULL COMMENT '行业(0-通用，1-特殊)', model_id CHAR(36) DEFAULT '' NULL COMMENT '潜力维度模型', remark TEXT NULL COMMENT '盘点备注', order_index INT UNSIGNED NULL COMMENT '排序', enabled TINYINT(3) UNSIGNED DEFAULT 1 NULL COMMENT '状态(0-禁用，1-启用)', audit_status TINYINT(3) UNSIGNED DEFAULT 0 NULL COMMENT '审核状态(0-草稿,1-已发布)', version VARCHAR(36) DEFAULT '' NULL COMMENT '版本号', create_user_id CHAR(36) DEFAULT '' NOT NULL COMMENT '创建人id', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) DEFAULT '' NOT NULL COMMENT '修改人id', update_time datetime NOT NULL COMMENT '修改时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间(数据库专用，禁止用于业务)', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间(数据库专用，禁止用于业务)', CONSTRAINT PK_RV_CLOUND_PROJECT PRIMARY KEY (id)) COMMENT='云方案管理表-快照表';

-- changeset liquibase:1733295843967-10
CREATE TABLE rv_clound_project_detail (id CHAR(36) NOT NULL COMMENT '云方案管理表id', org_id CHAR(36) NOT NULL COMMENT '机构id', clound_project_id CHAR(36) NOT NULL COMMENT '云方案管理表id', source_id CHAR(36) NOT NULL COMMENT '数据源id', dimension TINYINT(3) UNSIGNED DEFAULT 0 NULL COMMENT '维度(0-潜力)', create_user_id CHAR(36) DEFAULT '' NOT NULL COMMENT '创建人id', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) DEFAULT '' NOT NULL COMMENT '修改人id', update_time datetime NOT NULL COMMENT '修改时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间(数据库专用，禁止用于业务)', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间(数据库专用，禁止用于业务)', CONSTRAINT PK_RV_CLOUND_PROJECT_DETAIL PRIMARY KEY (id)) COMMENT='云方案管理明细表-快照表';

-- changeset liquibase:1733295843967-11
CREATE TABLE rv_clound_project_eval (id CHAR(36) NOT NULL COMMENT '云方案管理倍智测评表id', org_id CHAR(36) NOT NULL COMMENT '机构id', projet_detail_id CHAR(36) NOT NULL COMMENT '云方案管理明细表id', eval_name VARCHAR(200) NOT NULL COMMENT '测评名称', tool_id VARCHAR(200) NOT NULL COMMENT '倍智工具id', subject_num INT UNSIGNED NULL COMMENT '题目数量', finish_time INT UNSIGNED NULL COMMENT '完成时间(分钟)', limit_time TINYINT(3) UNSIGNED DEFAULT 0 NULL COMMENT '是否限时(0-不限时，1-每页限时)', eval_type TINYINT(3) UNSIGNED DEFAULT 0 NULL COMMENT '测评类型(0-倍智)', remark TEXT NULL COMMENT '测评介绍', pdf_url VARCHAR(500) DEFAULT '' NULL COMMENT 'pdf文件路径', create_user_id CHAR(36) DEFAULT '' NOT NULL COMMENT '创建人id', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) DEFAULT '' NOT NULL COMMENT '修改人id', update_time datetime NOT NULL COMMENT '修改时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间(数据库专用，禁止用于业务)', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间(数据库专用，禁止用于业务)', CONSTRAINT PK_RV_CLOUND_PROJECT_EVAL PRIMARY KEY (id)) COMMENT='云方案管理测评表-快照表';

-- changeset liquibase:1733295843967-12
CREATE TABLE rv_dict (id CHAR(36) NOT NULL COMMENT '字典表id', dict_type TINYINT(3) UNSIGNED NULL COMMENT '字典类型(1-绩效枚举)', dict_name VARCHAR(200) NULL COMMENT '字典名称', dict_value TINYINT(3) UNSIGNED NULL COMMENT '字典枚举', order_index INT UNSIGNED NULL COMMENT '排序', enabled TINYINT(3) UNSIGNED DEFAULT 1 NULL COMMENT '状态(0-禁用，1-启用)', create_user_id CHAR(36) NULL COMMENT '创建人id', create_time datetime DEFAULT NOW(3) NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NULL COMMENT '修改人id', update_time datetime DEFAULT NOW(3) NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间(数据库专用，禁止用于业务)', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间(数据库专用，禁止用于业务)', CONSTRAINT PK_RV_DICT PRIMARY KEY (id)) COMMENT='人才盘点字典表';

-- changeset liquibase:1733295843967-13
CREATE TABLE rv_dimension (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', dimension_name VARCHAR(50) DEFAULT '' NOT NULL COMMENT '维度名称', dimension_type TINYINT(3) DEFAULT 0 NOT NULL COMMENT '类型（1:绩效 2:能力，3:潜力，4-机构自定义）', dimension_enable TINYINT(3) DEFAULT 1 NOT NULL COMMENT '是否启用（0-禁用，1-启用）', remark VARCHAR(400) DEFAULT '' NOT NULL COMMENT '说明', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间(数据库专用，禁止用于业务)', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间(数据库专用，禁止用于业务)', CONSTRAINT PK_RV_DIMENSION PRIMARY KEY (id)) COMMENT='维度表';

-- changeset liquibase:1733295843967-14
CREATE TABLE rv_dimension_config (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', project_id CHAR(36) NOT NULL COMMENT '项目id', dimension_id CHAR(36) NOT NULL COMMENT '维度id', dimension_name VARCHAR(50) DEFAULT '' NOT NULL COMMENT '维度名称', dimension_status TINYINT(3) DEFAULT 0 NOT NULL COMMENT '维度状态（0-未配置，1-已配置）', model_id CHAR(36) DEFAULT '' NOT NULL COMMENT '模型id', tool_id CHAR(36) DEFAULT '' NOT NULL COMMENT '废弃，需查询rv_dimension_config_tool表', tool_type TINYINT(3) DEFAULT 0 NOT NULL COMMENT '盘点工具类型（0-未配置，1-绩效，2-测评，3-盘点数据导入，4：倍智测评）', order_index INT DEFAULT 0 NOT NULL COMMENT '排序，默认从0开始', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间(数据库专用，禁止用于业务)', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间(数据库专用，禁止用于业务)', CONSTRAINT PK_RV_DIMENSION_CONFIG PRIMARY KEY (id)) COMMENT='维度配置关系表';

-- changeset liquibase:1733295843967-15
CREATE TABLE rv_dimension_config_tool (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', project_id CHAR(36) NOT NULL COMMENT '项目id', dimension_config_id CHAR(36) NOT NULL COMMENT '维度配置id', tool_id CHAR(36) NOT NULL COMMENT '测评id', tool_type TINYINT(3) DEFAULT 0 NOT NULL COMMENT '盘点工具类型（1-绩效，2-测评，3-盘点数据导入，4：倍智测评）', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', tool_source TINYINT(3) DEFAULT 0 NOT NULL COMMENT '盘点工具来源（0-自建，1-同模,2-倍智测评,3:倍智测评数据源）', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间(数据库专用，禁止用于业务)', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间(数据库专用，禁止用于业务)', eval_ref_center TINYINT(3) DEFAULT 0 NULL COMMENT '盘点工具-同模测评是否来自于测评中心，0-否，1-是', CONSTRAINT PK_RV_DIMENSION_CONFIG_TOOL PRIMARY KEY (id)) COMMENT='盘点维度配置工具表';

-- changeset liquibase:1733295843967-16
CREATE TABLE rv_dimension_model_same (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', project_id CHAR(36) NOT NULL COMMENT '项目id', eval_id CHAR(36) NOT NULL COMMENT '测评id', model_id CHAR(36) DEFAULT '' NOT NULL COMMENT '模型id', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', dimension_config_id CHAR(36) NOT NULL COMMENT '维度配置id', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间(数据库专用，禁止用于业务)', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间(数据库专用，禁止用于业务)', CONSTRAINT PK_RV_DIMENSION_MODEL_SAME PRIMARY KEY (id)) COMMENT='盘点方案同模维度测评表';

-- changeset liquibase:1733295843967-17
CREATE TABLE rv_dmp (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', dmp_name VARCHAR(400) NOT NULL COMMENT '动态匹配项目名称', position_id CHAR(36) NOT NULL COMMENT '岗位id', position_name VARCHAR(400) NOT NULL COMMENT '冗余的岗位名称', grade_id VARCHAR(600) NULL COMMENT '职级id，非必填，多个职级id半角逗号分隔', grade_name VARCHAR(600) NULL COMMENT '冗余的职级名称，非必填，多个职级id半角逗号分隔', jq_id CHAR(36) NOT NULL COMMENT '任职资格id', jq_name VARCHAR(400) NOT NULL COMMENT '冗余的任职资格名称', start_time datetime NOT NULL COMMENT '项目开始时间', end_time datetime NOT NULL COMMENT '项目结束时间', remark VARCHAR(600) NULL COMMENT '项目说明', dmp_status TINYINT(3) NOT NULL COMMENT '项目状态（0-未发布<草稿>，1-未发布<定时>，2-进行中，3-已暂停，4-已结束，5-未发布<撤回>）', deleted TINYINT(3) DEFAULT 0 NOT NULL COMMENT '是否删除（0-未删除，1-已删除）', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间（数据库专用，禁止用于业务）', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间（数据库专用，禁止用于业务）', plan_id CHAR(36) NULL COMMENT '岗位配置id，来自于人才标准的岗位职级-任职资格配置表', pub_time datetime NULL COMMENT '发布时间', withdraw_time datetime NULL COMMENT '项目最近撤回时间，当项目状态为5-撤回,有值', CONSTRAINT PK_RV_DMP PRIMARY KEY (id)) COMMENT='人岗动态匹配项目';

-- changeset liquibase:1733295843967-18
CREATE TABLE rv_dmp_condition (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', dmp_id CHAR(36) NOT NULL COMMENT '人岗动态匹配项目id', task_id CHAR(36) NOT NULL COMMENT '任务方案id', dim_id CHAR(36) NOT NULL COMMENT '任务维度id', condition_group_id CHAR(36) NOT NULL COMMENT '任务维度条件组id', source_type TINYINT(3) NULL COMMENT '来源：0-自定义 1-任职资格本身自带的', label_type TINYINT(3) NOT NULL COMMENT '标签类型：1、指标 2、标签', label_id CHAR(36) NOT NULL COMMENT '标签/指标id', symbol TINYINT(3) NULL COMMENT '比较符号，1：等于，2：大于，3：大于等于，4：小于，5：小于等于，6：不等于，7：范围值', min_value VARCHAR(255) NULL COMMENT '范围比较最小值，比较符号是范围时有效', max_value VARCHAR(255) NULL COMMENT '范围比较最大值，比较符号是范围时有效', form_value VARCHAR(1100) NULL COMMENT '单一比较值，比较符号非范围值时有效，当标签类型是标签时，这里存储标签值id，当标签类型是指标时，这里存储具体的数值', remark VARCHAR(400) NULL COMMENT '描述说明', order_index INT DEFAULT 0 NULL COMMENT '排序字段', deleted TINYINT(3) DEFAULT 0 NOT NULL COMMENT '是否删除（0-未删除，1-已删除）', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间（数据库专用，禁止用于业务）', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间（数据库专用，禁止用于业务）', CONSTRAINT PK_RV_DMP_CONDITION PRIMARY KEY (id)) COMMENT='人岗动态匹配项目-维度条件';

-- changeset liquibase:1733295843967-19
CREATE TABLE rv_dmp_condition_group (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', dmp_id CHAR(36) NOT NULL COMMENT '人岗动态匹配项目id', task_id CHAR(36) NOT NULL COMMENT '任务方案id', dim_id CHAR(36) NOT NULL COMMENT '任务维度id', source_type TINYINT(3) NULL COMMENT '来源：0-自定义 1-任职资格本身自带的', match_type TINYINT(3) NULL COMMENT '匹配方式，1:满足其一(或)，2：全部满足(且)', order_index INT DEFAULT 0 NULL COMMENT '排序字段', deleted TINYINT(3) DEFAULT 0 NOT NULL COMMENT '是否删除（0-未删除，1-已删除）', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间（数据库专用，禁止用于业务）', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间（数据库专用，禁止用于业务）', CONSTRAINT PK_RV_DMP_CONDITION_GROUP PRIMARY KEY (id)) COMMENT='人岗动态匹配项目-维度条件组';

-- changeset liquibase:1733295843967-20
CREATE TABLE rv_dmp_conf (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', dmp_id CHAR(36) NOT NULL COMMENT '人岗动态匹配项目id', launch_type TINYINT(3) DEFAULT 0 NOT NULL COMMENT '发布方式（0-手动发布 1-定时自动发布）', launch_time_type TINYINT(3) NULL COMMENT '定时自动发布时间类型（0-按开始时间 1-自定义时间）', launch_time datetime NULL COMMENT '定时自动发布时设置的自定义发布时间', stop_type TINYINT(3) DEFAULT 0 NOT NULL COMMENT '结束方式（0-手动结束 1-自动结束）', stop_time_type TINYINT(3) NULL COMMENT '定时自动结束时间类型（0-按结束时间 1-自定义时间）', stop_time datetime NULL COMMENT '定时自动结束时设置的自定义结束时间', disable_eval TINYINT(3) DEFAULT 0 NOT NULL COMMENT '结束时间到期后，不允许学员继续参与评价：0-否 1-是', join_type TINYINT(3) DEFAULT 0 NOT NULL COMMENT '人员加入规则（0-手动加入 1-自动加入）', deleted TINYINT(3) DEFAULT 0 NOT NULL COMMENT '是否删除（0-未删除，1-已删除）', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间（数据库专用，禁止用于业务）', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间（数据库专用，禁止用于业务）', show_type TINYINT(3) DEFAULT 0 NULL COMMENT '展示任务类型（0-不展示动态任务，1-展示动态任务）', group_auto_exit INT DEFAULT 0 NOT NULL COMMENT '是否开启自动减人,0-否，1-开启', CONSTRAINT PK_RV_DMP_CONF PRIMARY KEY (id)) COMMENT='人岗动态匹配项目-配置';

-- changeset liquibase:1733295843967-21
CREATE TABLE rv_dmp_match_rule (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', dmp_id CHAR(36) NOT NULL COMMENT '人岗动态匹配项目id', match_type TINYINT(3) DEFAULT 0 NOT NULL COMMENT '匹配规则 0-按匹配率 1-按分值', score_level TINYINT(3) DEFAULT 0 NULL COMMENT '分值标准 0-百分制 1-十分制，匹配规则为按分值时生效', deleted TINYINT(3) DEFAULT 0 NOT NULL COMMENT '是否删除（0-未删除，1-已删除）', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间（数据库专用，禁止用于业务）', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间（数据库专用，禁止用于业务）', form_score TINYINT(3) DEFAULT 1 NULL COMMENT '是否使用表单得分进行计算，0-否 1-是', CONSTRAINT PK_RV_DMP_MATCH_RULE PRIMARY KEY (id)) COMMENT='人岗动态匹配项目-匹配规则';

-- changeset liquibase:1733295843967-22
CREATE TABLE rv_dmp_msg_conf (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', dmp_id CHAR(36) NOT NULL COMMENT '人岗动态匹配项目id', notification TINYINT(3) DEFAULT 1 NOT NULL COMMENT '是否发送消息通知（0-不发送，1-发送）', disable_flags INT UNSIGNED DEFAULT 0 NOT NULL COMMENT '禁用消息发送的标识，默认全部发送；采用位运算实现，第1位-不发送给学员，第2位-不发送给负责人，第3-不发送项目查看人员， 第4-不发送给项目管理人员，第5位-不发送给评估人，第6位-不发送启动提醒，第7位-不发送到期提醒', deleted TINYINT(3) DEFAULT 0 NOT NULL COMMENT '是否删除（0-未删除，1-已删除）', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间（数据库专用，禁止用于业务）', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间（数据库专用，禁止用于业务）', CONSTRAINT PK_RV_DMP_MSG_CONF PRIMARY KEY (id)) COMMENT='人岗动态匹配项目-消息配置';

-- changeset liquibase:1733295843967-23
CREATE TABLE rv_dmp_perm (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', dmp_id CHAR(36) NOT NULL COMMENT '人岗动态匹配项目id', user_id CHAR(36) NOT NULL COMMENT '负责人/被授权人员id', perm_type TINYINT(3) DEFAULT 0 NOT NULL COMMENT '权限类型（0-查看权限 1-管理权限 2-负责人）', deleted TINYINT(3) DEFAULT 0 NOT NULL COMMENT '是否删除（0-未删除，1-已删除）', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间（数据库专用，禁止用于业务）', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间（数据库专用，禁止用于业务）', CONSTRAINT PK_RV_DMP_PERM PRIMARY KEY (id)) COMMENT='人岗动态匹配项目-权限负责人表';

-- changeset liquibase:1733295843967-24
CREATE TABLE rv_dmp_pos (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', dmp_id CHAR(36) NOT NULL COMMENT '动态匹配项目id', position_id CHAR(36) NOT NULL COMMENT '岗位id', position_name VARCHAR(200) NOT NULL COMMENT '冗余的岗位名称', grade_id VARCHAR(600) NULL COMMENT '职级id，非必填，多个职级id半角逗号分隔', grade_name VARCHAR(600) NULL COMMENT '冗余的职级名称，非必填，多个职级id半角逗号分隔', plan_id CHAR(36) NULL COMMENT '岗位配置id，来自于人才标准的岗位职级-任职资格配置表', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间（数据库专用，禁止用于业务）', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间（数据库专用，禁止用于业务）', deleted TINYINT(3) DEFAULT 0 NOT NULL COMMENT '0-启用，1-删除', CONSTRAINT PK_RV_DMP_POS PRIMARY KEY (id)) COMMENT='人岗动态匹配岗位信息表';

-- changeset liquibase:1733295843967-25
CREATE TABLE rv_dmp_rule_layer (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', dmp_id CHAR(36) NOT NULL COMMENT '人岗动态匹配项目id', rule_id CHAR(36) NOT NULL COMMENT '匹配规则id', layer_name VARCHAR(100) NOT NULL COMMENT '分层名称（胜任、未胜任...）', layer_type TINYINT(3) DEFAULT 0 NOT NULL COMMENT '是否属于胜任分层, 0-未胜任 1-胜任', layer_value INT NOT NULL COMMENT '匹配率', order_index INT DEFAULT 0 NULL COMMENT '分层排序', deleted TINYINT(3) DEFAULT 0 NOT NULL COMMENT '是否删除（0-未删除，1-已删除）', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间（数据库专用，禁止用于业务）', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间（数据库专用，禁止用于业务）', CONSTRAINT PK_RV_DMP_RULE_LAYER PRIMARY KEY (id)) COMMENT='人岗动态匹配项目-分层规则';

-- changeset liquibase:1733295843967-26
CREATE TABLE rv_dmp_rule_score (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', dmp_id CHAR(36) NOT NULL COMMENT '人岗动态匹配项目id', rule_id CHAR(36) NOT NULL COMMENT '匹配规则id', dim_id CHAR(36) NOT NULL COMMENT '任务维度id', weight INT NOT NULL COMMENT '维度权重', deleted TINYINT(3) DEFAULT 0 NOT NULL COMMENT '是否删除（0-未删除，1-已删除）', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间（数据库专用，禁止用于业务）', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间（数据库专用，禁止用于业务）', CONSTRAINT PK_RV_DMP_RULE_SCORE PRIMARY KEY (id)) COMMENT='人岗动态匹配项目-维度分值设置(按分值计算匹配)';

-- changeset liquibase:1733295843967-27
CREATE TABLE rv_dmp_rule_score_detail (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', dmp_id CHAR(36) NOT NULL COMMENT '人岗动态匹配项目id', rule_id CHAR(36) NOT NULL COMMENT '匹配规则id', dim_id CHAR(36) NOT NULL COMMENT '任务维度id', score_id CHAR(36) NOT NULL COMMENT '维度分值设置id', pass_type TINYINT(3) NULL COMMENT '0-未达标 1-达标（当jq_item_type不是能力模型时有值）', skill_level_id CHAR(36) NULL COMMENT '能力等级id（当jq_item_type是能力等级模型时有值）', score INT DEFAULT 0 NULL COMMENT '匹配的分值', deleted TINYINT(3) DEFAULT 0 NOT NULL COMMENT '是否删除（0-未删除，1-已删除）', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间（数据库专用，禁止用于业务）', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间（数据库专用，禁止用于业务）', order_index INT DEFAULT 0 NOT NULL COMMENT '排序字段，从下到大排序', CONSTRAINT PK_RV_DMP_RULE_SCORE_DETAIL PRIMARY KEY (id)) COMMENT='人岗动态匹配项目-维度分值明细(按分值匹配)';

-- changeset liquibase:1733295843967-28
CREATE TABLE rv_dmp_task (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', dmp_id CHAR(36) NOT NULL COMMENT '人岗动态匹配项目id', task_name VARCHAR(400) NOT NULL COMMENT '任务名称', task_type TINYINT(3) DEFAULT 0 NOT NULL COMMENT '任务类型（0-动态匹配 1-表单评价）', eval_form_id CHAR(36) NULL COMMENT '测评的表单评价id，指向测评表单，当任务类型是表单评价时有值', active_status TINYINT(3) DEFAULT 0 NOT NULL COMMENT '启用状态（0-草稿，1-待启用，2-待启用（撤回），3-启用）,如果是表单，以测评中心状态为准，不要使用该字段', task_status TINYINT(3) DEFAULT 0 NOT NULL COMMENT '任务状态（0-未发布<草稿>，1-未发布<定时>，2-进行中，3-已暂停，4-已结束，5-未发布<撤回>）,如果是表单，以测评中心状态为准，不要使用该字段', remark VARCHAR(600) NULL COMMENT '任务说明', deleted TINYINT(3) DEFAULT 0 NOT NULL COMMENT '是否删除（0-未删除，1-已删除）', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间（数据库专用，禁止用于业务）', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间（数据库专用，禁止用于业务）', active_time datetime NULL COMMENT '启用时间，状态变为启用时的时间，只针对动态匹配类型的任务', CONSTRAINT PK_RV_DMP_TASK PRIMARY KEY (id)) COMMENT='人岗动态匹配项目-方案任务';

-- changeset liquibase:1733295843967-29
CREATE TABLE rv_dmp_task_dim (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', dmp_id CHAR(36) NOT NULL COMMENT '人岗动态匹配项目id', task_id CHAR(36) NOT NULL COMMENT '方案任务id', dim_type TINYINT(3) DEFAULT 0 NOT NULL COMMENT '维度类型：1、指标 2、标签 3、普通文本 4、能力模型，5、任务模型，6、资格证书 7、知识点', jq_skill_model_id CHAR(36) NULL COMMENT '能力模型id', jq_rt_model_id CHAR(36) NULL COMMENT '任务模型id', jq_dim_id CHAR(36) NOT NULL COMMENT '任职资格维度id', rule_id CHAR(36) NULL COMMENT '规则id，由规则设计器提供', rule_snap MEDIUMTEXT NULL COMMENT '规则快照', order_index INT DEFAULT 0 NULL COMMENT '维度排序字段', deleted TINYINT(3) DEFAULT 0 NOT NULL COMMENT '是否删除（0-未删除，1-已删除）', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间（数据库专用，禁止用于业务）', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间（数据库专用，禁止用于业务）', jq_dim_name VARCHAR(800) NULL COMMENT '冗余的维度名称', jq_cata_id CHAR(36) NULL COMMENT '任职资格维度所属的分类id', jq_cata_name VARCHAR(100) NULL COMMENT '任职资格维度所属的分类名称', rule_update_time datetime NULL COMMENT '更新快照的时间', jq_tpl_cat_id CHAR(36) NULL COMMENT '任职资格维度映射的模版分类id', jq_tpl_cat_name VARCHAR(100) NULL COMMENT '任职资格维度映射的模版分类名称', CONSTRAINT PK_RV_DMP_TASK_DIM PRIMARY KEY (id)) COMMENT='人岗动态匹配项目-维度';

-- changeset liquibase:1733295843967-30
CREATE TABLE rv_dmp_task_job (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', dmp_id CHAR(36) NOT NULL COMMENT '人岗动态匹配项目id', task_id CHAR(36) NOT NULL COMMENT '任务id', job_status TINYINT(3) NULL COMMENT '当前计算job的状态，0-等待计算 1-计算中 2-计算完成 3-异常', calc_wait_time datetime NULL COMMENT '放入计算队列的时间', calc_start_time datetime NULL COMMENT '开始计算的时间', calc_end_time datetime NULL COMMENT '结束结算的时间', exception MEDIUMTEXT NULL COMMENT '异常原因，当job_status=3时，此处有值', deleted TINYINT(3) DEFAULT 0 NOT NULL COMMENT '是否删除（0-未删除，1-已删除）', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间（数据库专用，禁止用于业务）', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间（数据库专用，禁止用于业务）', CONSTRAINT PK_RV_DMP_TASK_JOB PRIMARY KEY (id)) COMMENT='人岗动态匹配项目-任务计算job执行记录';

-- changeset liquibase:1733295843967-31
CREATE TABLE rv_dmp_user (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', dmp_id CHAR(36) NOT NULL COMMENT '人岗动态匹配项目id', user_id CHAR(36) NOT NULL COMMENT '人员id', join_time datetime NOT NULL COMMENT '加入时间', join_type TINYINT(3) DEFAULT 0 NOT NULL COMMENT '加入方式（0-手动加入 1-自动加入）', deleted TINYINT(3) DEFAULT 0 NOT NULL COMMENT '是否删除（0-未删除，1-已删除）', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间（数据库专用，禁止用于业务）', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间（数据库专用，禁止用于业务）', CONSTRAINT PK_RV_DMP_USER PRIMARY KEY (id)) COMMENT='人岗动态匹配项目-学员';

-- changeset liquibase:1733295843967-32
CREATE TABLE rv_dmp_user_auto_group (id CHAR(36) NOT NULL COMMENT '主键id', org_id CHAR(36) NOT NULL COMMENT '机构id', dmp_id CHAR(36) NOT NULL COMMENT '人岗动态匹配项目id', group_id CHAR(36) NOT NULL COMMENT '动态用户组id', group_name CHAR(200) NULL COMMENT '动态用户组名称', sync_flag TINYINT(3) DEFAULT 0 NOT NULL COMMENT '是否已经同步人员，0-未同步，1-同步', deleted TINYINT(3) DEFAULT 0 NOT NULL COMMENT '0-未删除，1-删除', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NOT NULL COMMENT '数据创建时间（数据库专用，禁止用于业务）', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NOT NULL COMMENT '数据修改时间（数据库专用，禁止用于业务）', CONSTRAINT PK_RV_DMP_USER_AUTO_GROUP PRIMARY KEY (id)) COMMENT='人岗动态匹配项目-动态加人规则';

-- changeset liquibase:1733295843967-33
CREATE TABLE rv_dmp_user_dim_detail (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', dmp_id CHAR(36) NOT NULL COMMENT '人岗动态匹配项目id', task_id CHAR(36) NOT NULL COMMENT '方案任务id', user_id CHAR(36) NOT NULL COMMENT '学员id', dim_id CHAR(36) NOT NULL COMMENT '维度id，指向rv_dmp_task_dim.id', jq_dim_id CHAR(36) NOT NULL COMMENT '冗余任职资格维度id', condition_id CHAR(36) NOT NULL COMMENT '维度条件id，指向规则json中的某一个条件主键', label_type TINYINT(3) NOT NULL COMMENT '标签类型：1-指标 2-标签', label_id CHAR(36) NOT NULL COMMENT '标签/指标id', label_value_id VARCHAR(1100) NULL COMMENT '标签/指标值id，多个值之间使用半角逗号分隔', label_value TEXT NULL COMMENT '标签/指标值，多个值之间使用半角逗号分隔', matched TINYINT(3) DEFAULT 0 NOT NULL COMMENT '是否达标：0-不达标 1-达标 2-异常不达标（异常，可能因维度在任职资格中被删除找不到了）', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间（数据库专用，禁止用于业务）', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间（数据库专用，禁止用于业务）', CONSTRAINT PK_RV_DMP_USER_DIM_DETAIL PRIMARY KEY (id)) COMMENT='人岗动态匹配项目-员工维度匹配明细';

-- changeset liquibase:1733295843967-34
CREATE TABLE rv_dmp_user_dim_result (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', dmp_id CHAR(36) NOT NULL COMMENT '人岗动态匹配项目id', task_id CHAR(36) NOT NULL COMMENT '方案任务id', user_id CHAR(36) NOT NULL COMMENT '学员id', dim_id CHAR(36) NOT NULL COMMENT '维度id，指向rv_dmp_task_dim.id', jq_dim_id CHAR(36) NOT NULL COMMENT '任职资格维度id', matched TINYINT(3) DEFAULT 0 NOT NULL COMMENT '是否达标：0-不达标 1-达标 2-异常不达标（异常，可能因维度在任职资格中被删除找不到了）', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间（数据库专用，禁止用于业务）', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间（数据库专用，禁止用于业务）', form_score DECIMAL(11, 4) NULL COMMENT '学员该维度的表单综合分，由测评提供', skill_level_id CHAR(36) NULL COMMENT '学员该维度的表单综合分匹配到的能力等级，由测评提供', exception MEDIUMTEXT NULL COMMENT '维度异常原因，当matched=2时有值', score DECIMAL(11, 4) NULL COMMENT 'DMP规则中设置的维度得分（根据达标、未达标、匹配能力层级之后获得的分）', weight_score DECIMAL(11, 4) NULL COMMENT '经过规则匹配转换之后的最终维度权重得分，即 score * rv_dmp_rule_score.weight', form_clac_time datetime NULL COMMENT '计算出结果的具体时间点（动态匹配任务的维度该值与create_time字段一致，表单任务的维度该值由测评方给出）', CONSTRAINT PK_RV_DMP_USER_DIM_RESULT PRIMARY KEY (id)) COMMENT='人岗动态匹配项目-个人维度匹配结果';

-- changeset liquibase:1733295843967-35
CREATE TABLE rv_dmp_user_result (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', dmp_id CHAR(36) NOT NULL COMMENT '人岗动态匹配项目id', user_id CHAR(36) NOT NULL COMMENT '学员id', layer_id CHAR(36) NOT NULL COMMENT '匹配的层级id, 指向rv_dmp_rule_layer.id', competent TINYINT(3) DEFAULT 0 NOT NULL COMMENT '是否胜任, 0-不胜任（低于胜任分层） 1-胜任（位于胜任及以上的分层）', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间（数据库专用，禁止用于业务）', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间（数据库专用，禁止用于业务）', score DECIMAL(11, 4) NULL COMMENT '匹配率or分值', complete TINYINT(3) DEFAULT 0 NULL COMMENT '项目完成状态：0-未完成 1-已完成 2-不在任何任务中', CONSTRAINT PK_RV_DMP_USER_RESULT PRIMARY KEY (id)) COMMENT='人岗动态匹配项目-个人胜任结果';

-- changeset liquibase:1733295843967-36
CREATE TABLE rv_dmp_user_task_result (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', dmp_id CHAR(36) NOT NULL COMMENT '人岗动态匹配项目id', task_id CHAR(36) NOT NULL COMMENT '方案任务id', user_id CHAR(36) NOT NULL COMMENT '学员id', done TINYINT(3) DEFAULT 0 NOT NULL COMMENT '是否完成：0-未完成 1-已完成（动态匹配任务默认创建及完成），2-不在该任务中（表单项目可以从学员中挑选部分人员）', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间(数据库专用，禁止用于业务)', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间(数据库专用，禁止用于业务)', CONSTRAINT PK_RV_DMP_USER_TASK_RESULT PRIMARY KEY (id)) COMMENT='人岗动态匹配项目-个人任务完成情况';

-- changeset liquibase:1733295843967-37
CREATE TABLE rv_meeting_user (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', project_id CHAR(36) NOT NULL COMMENT '盘点项目id', meeting_id CHAR(36) NOT NULL COMMENT '校准会id', user_id CHAR(36) NOT NULL COMMENT '人员id', suggestion VARCHAR(800) DEFAULT '' NULL COMMENT '发展建议', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间(数据库专用，禁止用于业务)', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间(数据库专用，禁止用于业务)', CONSTRAINT PK_RV_MEETING_USER PRIMARY KEY (id)) COMMENT='校准会人员关系表';

-- changeset liquibase:1733295843967-38
CREATE TABLE rv_meeting_user_remark (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', project_id CHAR(36) NOT NULL COMMENT '盘点项目id', meeting_id CHAR(36) NOT NULL COMMENT '校准会id', user_id CHAR(36) NOT NULL COMMENT '人员id', remark VARCHAR(2500) DEFAULT '' NOT NULL COMMENT '校准备注', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间（数据库专用，禁止用于业务）', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间（数据库专用，禁止用于业务）', CONSTRAINT PK_RV_MEETING_USER_REMARK PRIMARY KEY (id)) COMMENT='盘点校准备注';

-- changeset liquibase:1733295843967-39
CREATE TABLE rv_org_setting (id CHAR(36) DEFAULT '' NOT NULL COMMENT 'id', org_id CHAR(36) DEFAULT '' NOT NULL COMMENT '机构id', grade_set TINYINT(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '机构是否启用自定义绩效等级：0-不开启 1-开启;默认不开启', create_user_id CHAR(36) DEFAULT '' NULL COMMENT '创建人id', create_time datetime DEFAULT NOW(3) NOT NULL COMMENT '创建日期', update_user_id CHAR(36) DEFAULT '' NULL COMMENT '更新人id', update_time datetime DEFAULT NOW(3) NOT NULL COMMENT '更新日期', db_create_time datetime DEFAULT NOW(3) NOT NULL COMMENT '数据创建时间（数据库专用，禁止用于业务）', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NOT NULL COMMENT '数据修改时间（数据库专用，禁止用于业务）', CONSTRAINT PK_RV_ORG_SETTING PRIMARY KEY (id)) COMMENT='人才盘点机构设置';

-- changeset liquibase:1733295843967-40
CREATE TABLE rv_performance (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', period_id CHAR(36) NOT NULL COMMENT '绩效周期id', period_level VARCHAR(50) NOT NULL COMMENT '绩效等级/结果（A,B,C,D,S,高,中,低...）', user_id CHAR(36) NOT NULL COMMENT '用户id', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间(数据库专用，禁止用于业务)', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间(数据库专用，禁止用于业务)', perf_score DECIMAL(10, 2) NULL COMMENT '绩效总分', perf_point DECIMAL(10, 2) NULL COMMENT '绩效得分', perf_activity VARCHAR(255) NULL COMMENT '绩效活动', perf_third_activity_id VARCHAR(100) NULL COMMENT '绩效活动 id', CONSTRAINT PK_RV_PERFORMANCE PRIMARY KEY (id)) COMMENT='绩效表';

-- changeset liquibase:1733295843967-41
CREATE TABLE rv_performance_grade (id CHAR(36) DEFAULT '' NOT NULL COMMENT 'id', org_id CHAR(36) DEFAULT '' NOT NULL COMMENT '机构id', grade_name VARCHAR(30) DEFAULT '' NOT NULL COMMENT '绩效等级名称', grade_value INT UNSIGNED DEFAULT 100 NOT NULL COMMENT '绩效等级排序，从100开始，由于高绩效排序在前,100代表最高绩效等级', order_index INT UNSIGNED DEFAULT 0 NULL COMMENT '排序', deleted TINYINT(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '是否删除(0-未删除,1-已删除)', create_user_id CHAR(36) DEFAULT '' NULL COMMENT '创建人id', create_time datetime DEFAULT NOW(3) NOT NULL COMMENT '创建日期', update_user_id CHAR(36) DEFAULT '' NULL COMMENT '更新人id', update_time datetime DEFAULT NOW(3) NOT NULL COMMENT '更新日期', db_create_time datetime DEFAULT NOW(3) NOT NULL COMMENT '数据创建时间（数据库专用，禁止用于业务）', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NOT NULL COMMENT '数据修改时间（数据库专用，禁止用于业务）', state TINYINT(3) UNSIGNED DEFAULT 1 NOT NULL COMMENT '0-禁用/1-启用', CONSTRAINT PK_RV_PERFORMANCE_GRADE PRIMARY KEY (id)) COMMENT='人才盘点机构自定义绩效等级';

-- changeset liquibase:1733295843967-42
CREATE TABLE rv_performance_period (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', period_name VARCHAR(50) NOT NULL COMMENT '周期名称', order_index INT NOT NULL COMMENT '排序', update_time datetime NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间(数据库专用，禁止用于业务)', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间(数据库专用，禁止用于业务)', cycle TINYINT(3) UNSIGNED NULL COMMENT '绩效周期类型（老数据该字段可能为空）：0-月度绩效, 1-季度绩效, 2-半年度，3-年度', period INT UNSIGNED NULL COMMENT '月度:1-12, 季度:1-4, 半年度:1-2,年份:20xx', yearly INT NULL COMMENT '绩效年份，格式：YYYY', deleted TINYINT(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '是否删除(0-否,1-是)', CONSTRAINT PK_RV_PERFORMANCE_PERIOD PRIMARY KEY (id)) COMMENT='绩效周期表';

-- changeset liquibase:1733295843967-43
CREATE TABLE rv_prj_impt_type (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', prj_id CHAR(36) NOT NULL COMMENT '盘点项目id', dim_id CHAR(36) NOT NULL COMMENT '盘点维度id', impt_type TINYINT(3) DEFAULT 0 NOT NULL COMMENT '导入类型：1-维度等级，2-维度模型评分', deleted TINYINT(3) DEFAULT 0 NOT NULL COMMENT '是否删除（0-未删除，1-已删除）', create_user_id CHAR(36) NULL COMMENT '创建人id', create_time datetime DEFAULT NOW(3) NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NULL COMMENT '修改人id', update_time datetime DEFAULT NOW(3) NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NOT NULL COMMENT '数据创建时间（数据库专用，禁止用于业务）', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NOT NULL COMMENT '数据修改时间（数据库专用，禁止用于业务）', CONSTRAINT PK_RV_PRJ_IMPT_TYPE PRIMARY KEY (id)) COMMENT='盘点项目导入结果记录';

-- changeset liquibase:1733295843967-44
CREATE TABLE rv_prj_manager (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', project_id CHAR(36) NOT NULL COMMENT '盘点项目id', user_id CHAR(36) NOT NULL COMMENT '负责人id', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间（数据库专用，禁止用于业务）', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间（数据库专用，禁止用于业务）', CONSTRAINT PK_RV_PRJ_MANAGER PRIMARY KEY (id)) COMMENT='盘点负责人表';

-- changeset liquibase:1733295843967-45
CREATE TABLE rv_prj_result_config (id CHAR(36) DEFAULT '' NOT NULL COMMENT 'id', org_id CHAR(36) DEFAULT '' NOT NULL COMMENT '机构id', label_name VARCHAR(200) NULL COMMENT '人才定义名称', label_key VARCHAR(200) NULL COMMENT '人才定义名称国际化key', order_index INT UNSIGNED DEFAULT 0 NULL COMMENT '排序', logo_url VARCHAR(500) NULL COMMENT 'logo图片地址', deleted TINYINT(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '是否删除(0-未删除,1-已删除)', create_user_id CHAR(36) DEFAULT '' NULL COMMENT '创建人id', create_time datetime DEFAULT NOW(3) NOT NULL COMMENT '创建日期', update_user_id CHAR(36) DEFAULT '' NULL COMMENT '更新人id', update_time datetime DEFAULT NOW(3) NOT NULL COMMENT '更新日期', db_create_time datetime DEFAULT NOW(3) NOT NULL COMMENT '数据创建时间（数据库专用，禁止用于业务）', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NOT NULL COMMENT '数据修改时间（数据库专用，禁止用于业务）', CONSTRAINT PK_RV_PRJ_RESULT_CONFIG PRIMARY KEY (id)) COMMENT='人才定义配置表';

-- changeset liquibase:1733295843967-46
CREATE TABLE rv_prj_result_rule (id CHAR(36) DEFAULT '' NOT NULL COMMENT 'id', org_id CHAR(36) DEFAULT '' NOT NULL COMMENT '机构id', result_config_id CHAR(36) DEFAULT '' NOT NULL COMMENT '人才定义表id', rule_config JSON NULL COMMENT '规则配置，JSON存储', deleted TINYINT(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '是否删除(0-未删除,1-已删除)', create_user_id CHAR(36) DEFAULT '' NULL COMMENT '创建人id', create_time datetime DEFAULT NOW(3) NOT NULL COMMENT '创建日期', update_user_id CHAR(36) DEFAULT '' NULL COMMENT '更新人id', update_time datetime DEFAULT NOW(3) NOT NULL COMMENT '更新日期', db_create_time datetime DEFAULT NOW(3) NOT NULL COMMENT '数据创建时间（数据库专用，禁止用于业务）', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NOT NULL COMMENT '数据修改时间（数据库专用，禁止用于业务）', rule_expression TEXT NULL COMMENT '规则表达式', rule_expression_show TEXT NULL COMMENT '规则表达式供前端使用', CONSTRAINT PK_RV_PRJ_RESULT_RULE PRIMARY KEY (id)) COMMENT='人才定义规则表';

-- changeset liquibase:1733295843967-47
CREATE TABLE rv_prj_rule (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', dimension_config_id CHAR(36) NOT NULL COMMENT '维度配置关系主键id', expression VARCHAR(500) DEFAULT '' NOT NULL COMMENT '公式;例如(字段作废移到rv_prj_rule_expression表中)', expression_desc VARCHAR(500) DEFAULT '' NOT NULL COMMENT '公式说明(字段作废移到rv_prj_rule_expression表中)', expression_show VARCHAR(500) DEFAULT '' NULL COMMENT '公式用于页面显示(字段作废移到rv_prj_rule_expression表中)', expression_array VARCHAR(500) DEFAULT '' NULL COMMENT '前端公式数组用于编辑(字段作废移到rv_prj_rule_expression表中)', expression_submit_List VARCHAR(500) DEFAULT '' NULL COMMENT '前端公式原始数据(字段作废移到rv_prj_rule_expression表中)', rule_remark VARCHAR(100) DEFAULT '' NOT NULL COMMENT '规则说明', class_type TINYINT(3) DEFAULT 0 NOT NULL COMMENT '分级类型（0-枚举，1-百分比，2-绝对值）', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间(数据库专用，禁止用于业务)', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间(数据库专用，禁止用于业务)', compute_priority TINYINT(3) DEFAULT 0 NULL COMMENT '规则计算优先级，默认高等级优先，0-高等级优先， 1-低等级优先', CONSTRAINT PK_RV_PRJ_RULE PRIMARY KEY (id)) COMMENT='盘点计算规则表';

-- changeset liquibase:1733295843967-48
CREATE TABLE rv_prj_rule_expression (id CHAR(36) NOT NULL COMMENT '公式id', prj_rule_id CHAR(36) NOT NULL COMMENT '盘点计算规则表id', org_id CHAR(36) NOT NULL COMMENT '机构id', expression TEXT NULL COMMENT '公式;例如：（1+2-3*4）', expression_desc TEXT NULL COMMENT '公式说明', expression_show TEXT NULL COMMENT '公式用于页面显示', expression_array TEXT NULL COMMENT '前端公式数组用于编辑', expression_submit_List TEXT NULL COMMENT '前端公式原始数据（前端使用）', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间(数据库专用，禁止用于业务)', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间(数据库专用，禁止用于业务)', CONSTRAINT PK_RV_PRJ_RULE_EXPRESSION PRIMARY KEY (id)) COMMENT='规则公式表';

-- changeset liquibase:1733295843967-49
CREATE TABLE rv_prj_user (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', project_id CHAR(36) NOT NULL COMMENT '盘点项目id', user_id CHAR(36) NOT NULL COMMENT '人员id', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', suggestion VARCHAR(800) DEFAULT '' NOT NULL COMMENT '发展建议', prj_result TINYINT(3) UNSIGNED DEFAULT 0 NULL COMMENT '盘点结果：取自机构自定义人才标签（默认：0-未知，2-中坚力量, 3-待提升人员, 1-优秀人才）', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间(数据库专用，禁止用于业务)', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间(数据库专用，禁止用于业务)', CONSTRAINT PK_RV_PRJ_USER PRIMARY KEY (id)) COMMENT='盘点人员表';

-- changeset liquibase:1733295843967-50
CREATE TABLE rv_prj_user_calc_import (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', project_id CHAR(36) NOT NULL COMMENT '项目id', user_id CHAR(36) NOT NULL COMMENT '用户id', dimension_id CHAR(36) NOT NULL COMMENT '维度id', dimension_config_id CHAR(36) NOT NULL COMMENT '维度配置id', skill_id CHAR(36) NOT NULL COMMENT '能力ID', score DECIMAL(6, 2) NULL COMMENT '能力分值', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间(数据库专用，禁止用于业务)', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间(数据库专用，禁止用于业务)', CONSTRAINT PK_RV_PRJ_USER_CALC_IMPORT PRIMARY KEY (id)) COMMENT='导入盘点数据';

-- changeset liquibase:1733295843967-51
CREATE TABLE rv_prj_user_calc_result (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', project_id CHAR(36) NOT NULL COMMENT '盘点项目id', dimension_id CHAR(36) NOT NULL COMMENT '维度id', user_id CHAR(36) NOT NULL COMMENT '用户id', original_level TINYINT(3) DEFAULT 0 NOT NULL COMMENT '原始结果（0-默认，1-低，2-中，3-高）', original_score DECIMAL(6, 2) DEFAULT -1 NOT NULL COMMENT '原始分数', calibration_level TINYINT(3) DEFAULT 0 NOT NULL COMMENT '校准结果（0-默认，1-低，2-中，3-高）', calibration_score DECIMAL(6, 2) DEFAULT -1 NOT NULL COMMENT '校准分数', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间(数据库专用，禁止用于业务)', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间(数据库专用，禁止用于业务)', original_perf_point DECIMAL(10, 2) NULL COMMENT '原始绩效分数', calibration_perf_point DECIMAL(10, 2) NULL COMMENT '校准后绩效分数', perf_score DECIMAL(10, 2) NULL COMMENT '绩效总分', CONSTRAINT PK_RV_PRJ_USER_CALC_RESULT PRIMARY KEY (id)) COMMENT='用户盘点校准结果表';

-- changeset liquibase:1733295843967-52
CREATE TABLE rv_project (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', project_name VARCHAR(50) NOT NULL COMMENT '项目名称', project_category_id CHAR(36) NOT NULL COMMENT '项目分类id', project_type TINYINT(3) DEFAULT 0 NOT NULL COMMENT '项目类型：0-自建，1-云端方案创建', start_time datetime NULL COMMENT '项目开始时间', end_time datetime NULL COMMENT '项目结束时间', remark VARCHAR(400) NULL COMMENT '项目盘点目标', progress DECIMAL(6, 2) NULL COMMENT '项目进度', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', project_status TINYINT(3) NOT NULL COMMENT '项目状态（0-未发布草稿，1-未开始，2-进行中，3-已结束）', deleted TINYINT(3) DEFAULT 0 NOT NULL COMMENT '是否删除（0-未删除，1-已删除）', start_prj_time datetime NULL COMMENT '项目启动时间', deleted_prj_time datetime NULL COMMENT '项目删除时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间(数据库专用，禁止用于业务)', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间(数据库专用，禁止用于业务)', CONSTRAINT PK_RV_PROJECT PRIMARY KEY (id)) COMMENT='盘点项目表';

-- changeset liquibase:1733295843967-53
CREATE TABLE rv_project_label (id CHAR(36) NOT NULL COMMENT 'id', org_id CHAR(36) NOT NULL COMMENT '机构id', label_name VARCHAR(200) NOT NULL COMMENT '标签名称', label_sort TINYINT(3) UNSIGNED DEFAULT 0 NULL COMMENT '标签位置，取值范围为1-9标记九宫格的九个位置', create_user_id CHAR(36) DEFAULT '' NOT NULL COMMENT '创建人id', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) DEFAULT '' NOT NULL COMMENT '修改人id', update_time datetime NOT NULL COMMENT '修改时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间(数据库专用，禁止用于业务)', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间(数据库专用，禁止用于业务)', CONSTRAINT PK_RV_PROJECT_LABEL PRIMARY KEY (id)) COMMENT='盘点项目九宫格标签表';

-- changeset liquibase:1733295843967-54
CREATE TABLE rv_project_training (id CHAR(36) NOT NULL COMMENT 'id', org_id CHAR(36) DEFAULT '' NOT NULL COMMENT '机构id', project_id CHAR(36) DEFAULT '' NOT NULL COMMENT '盘点项目id', training_id CHAR(36) DEFAULT '' NOT NULL COMMENT '培训项目id', deleted TINYINT(3) UNSIGNED DEFAULT 0 NULL COMMENT '是否删除(0-否,1-是)', create_user_id CHAR(36) DEFAULT '' NOT NULL COMMENT '创建人id', create_time datetime DEFAULT NOW(3) NOT NULL COMMENT '创建时间', update_user_id CHAR(36) DEFAULT '' NOT NULL COMMENT '修改人id', update_time datetime DEFAULT NOW(3) NOT NULL COMMENT '修改时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间(数据库专用，禁止用于业务)', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间(数据库专用，禁止用于业务)', CONSTRAINT PK_RV_PROJECT_TRAINING PRIMARY KEY (id)) COMMENT='盘点项目关联测训项目关系表';

-- changeset liquibase:1733295843967-55
CREATE TABLE rv_reward_punishment_history (id VARCHAR(100) NOT NULL COMMENT '主键Id', third_user_id VARCHAR(100) NOT NULL COMMENT '用户id', org_id VARCHAR(100) NOT NULL COMMENT '机构Id', rp_type TINYINT(3) NOT NULL COMMENT '奖惩类型（1-奖项 2-惩罚）', rp_name VARCHAR(255) NULL COMMENT '奖惩名称', acq_time datetime NULL COMMENT '获得时间', pub_from VARCHAR(255) NULL COMMENT '发布方', deleted TINYINT(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '是否删除(0-否,1-是)', create_time datetime DEFAULT NOW(3) NULL COMMENT '创建时间', update_time datetime DEFAULT NOW(3) NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NOT NULL COMMENT '数据创建时间（数据库专用，禁止用于业务）', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NOT NULL COMMENT '数据修改时间（数据库专用，禁止用于业务）', CONSTRAINT PK_RV_REWARD_PUNISHMENT_HISTORY PRIMARY KEY (id)) COMMENT='奖惩信息历史';

-- changeset liquibase:1733295843967-56
CREATE TABLE rv_rule_condition (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', project_id CHAR(36) NOT NULL COMMENT '盘点项目id', dimension_id CHAR(36) NOT NULL COMMENT '维度id', rule_id CHAR(36) DEFAULT '' NOT NULL COMMENT '计算规则表主键id', symbol TINYINT(3) DEFAULT 0 NOT NULL COMMENT '符号（-2-小于等于，-1-小于，0-等于，1-大于，2-大于等于）', condition_level TINYINT(3) DEFAULT 1 NOT NULL COMMENT '等级（1-低，2-中，3-高）', symbol_value DECIMAL(12, 2) DEFAULT 0 NOT NULL COMMENT '数值', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间(数据库专用，禁止用于业务)', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间(数据库专用，禁止用于业务)', CONSTRAINT PK_RV_RULE_CONDITION PRIMARY KEY (id)) COMMENT='规则分级条件表';

-- changeset liquibase:1733295843967-57
CREATE TABLE rv_user_data (id CHAR(36) NOT NULL COMMENT 'id', org_id CHAR(36) NOT NULL COMMENT '机构id', project_id CHAR(36) NOT NULL COMMENT '项目Id', user_id CHAR(36) NOT NULL COMMENT '用户id', latest_result TINYINT(3) UNSIGNED DEFAULT 0 NULL COMMENT '盘点结果：取自机构自定义人才标签（默认：0-未知，2-中坚力量, 3-待提升人员, 1-优秀人才', create_user_id CHAR(36) DEFAULT '' NOT NULL COMMENT '创建人id', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) DEFAULT '' NOT NULL COMMENT '修改人id', update_time datetime NOT NULL COMMENT '修改时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间(数据库专用，禁止用于业务)', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间(数据库专用，禁止用于业务)', comp_dim_cnt INT DEFAULT 0 NOT NULL COMMENT '完成计算的维度数', all_dim_comp TINYINT(3) DEFAULT 0 NULL COMMENT '是否所有维度皆计算完成', CONSTRAINT PK_RV_USER_DATA PRIMARY KEY (id)) COMMENT='盘点用户数据';

-- changeset liquibase:1733295843967-58
CREATE TABLE rv_user_ext (id VARCHAR(100) NOT NULL COMMENT '主键Id，无业务意义，不等同于udp_user.id', third_user_id VARCHAR(100) NOT NULL COMMENT '三方用户id', org_id VARCHAR(100) NOT NULL COMMENT '机构Id', enabled TINYINT(3) NULL COMMENT '禁用/启用(0-禁用 1-启用)', user_id VARCHAR(100) NOT NULL COMMENT '绚星平台用户ID，等同于udp_user.id', manager TINYINT(3) NULL COMMENT '是否管理者(0-否 1-是)', key_position TINYINT(3) NULL COMMENT '是否关键岗位(0-否 1-是)', grade_level VARCHAR(255) NULL COMMENT '职等', residence_address VARCHAR(255) NULL COMMENT '常住地', prof_certs VARCHAR(800) NULL COMMENT '职业资格证书，多个证书使用半角分号分割', deleted TINYINT(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '是否删除(0-否,1-是)', create_time datetime DEFAULT NOW(3) NULL COMMENT '创建时间', update_time datetime DEFAULT NOW(3) NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NOT NULL COMMENT '数据创建时间（数据库专用，禁止用于业务）', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NOT NULL COMMENT '数据修改时间（数据库专用，禁止用于业务）', CONSTRAINT PK_RV_USER_EXT PRIMARY KEY (id)) COMMENT='用户扩展信息表';

-- changeset liquibase:1733295843967-59
CREATE TABLE rv_user_focus (id CHAR(36) NOT NULL COMMENT '主键', org_id CHAR(36) NOT NULL COMMENT '机构id', user_id CHAR(36) NOT NULL COMMENT '人员id', target_id CHAR(36) NOT NULL COMMENT '盘点项目id或动态匹配项目id', target_type TINYINT(3) DEFAULT 0 NOT NULL COMMENT '项目类型（0-盘点项目 1-动态匹配项目）', deleted TINYINT(3) DEFAULT 0 NOT NULL COMMENT '是否删除（0-未删除，1-已删除）', create_user_id CHAR(36) NOT NULL COMMENT '创建人主键', create_time datetime NOT NULL COMMENT '创建时间', update_user_id CHAR(36) NOT NULL COMMENT '更新人主键', update_time datetime NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NULL COMMENT '数据创建时间（数据库专用，禁止用于业务）', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NULL COMMENT '数据修改时间（数据库专用，禁止用于业务）', CONSTRAINT PK_RV_USER_FOCUS PRIMARY KEY (id)) COMMENT='我关注的项目盘点和动态盘点';

-- changeset liquibase:1733295843967-60
CREATE TABLE sprv_sys_operate_log (id BIGINT UNSIGNED NOT NULL COMMENT '主键', org_id VARCHAR(36) NOT NULL COMMENT '机构ID', deleted TINYINT(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '删除标记位;0：未删除 1：已删除', create_time datetime DEFAULT NOW(3) NOT NULL COMMENT '创建时间', update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NOT NULL COMMENT '更新时间', db_create_time datetime DEFAULT NOW(3) NOT NULL COMMENT '数据创建时间', db_update_time datetime DEFAULT CURRENT_TIMESTAMP(3) on update CURRENT_TIMESTAMP(3) NOT NULL COMMENT '数据更新时间', cluster VARCHAR(32) NULL COMMENT '集群泳道', operate_type TINYINT(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '操作类型', operate_key VARCHAR(64) NOT NULL COMMENT '操作key(异常类名)', `description` VARCHAR(256) NULL COMMENT '描述(日志内容和业务异常行号)', detail MEDIUMTEXT NULL COMMENT '详情数据(日志内容+异常栈)', observer VARCHAR(32) NULL COMMENT '关注人', CONSTRAINT PK_SPRV_SYS_OPERATE_LOG PRIMARY KEY (id)) COMMENT='操作日志';

-- changeset liquibase:1733295843967-61
CREATE INDEX idx_career_history_action_name ON rv_career_history(action_name);

-- changeset liquibase:1733295843967-62
CREATE INDEX idx_create ON sprv_sys_operate_log(create_time);

-- changeset liquibase:1733295843967-63
CREATE INDEX idx_create_time ON rv_ai_tool_message(create_time, qa_type, checked);

-- changeset liquibase:1733295843967-64
CREATE INDEX idx_dict_type ON rv_dict(dict_type);

-- changeset liquibase:1733295843967-65
CREATE INDEX idx_did_tid ON rv_dmp_task(org_id, dmp_id, task_name);

-- changeset liquibase:1733295843967-66
CREATE INDEX idx_dmp_cgid_oid ON rv_dmp_condition(condition_group_id, org_id);

-- changeset liquibase:1733295843967-67
CREATE INDEX idx_dmp_dimid_oid ON rv_dmp_condition_group(dim_id, org_id);

-- changeset liquibase:1733295843967-68
CREATE INDEX idx_dmp_oid_del_uid ON rv_dmp_user(org_id, dmp_id, deleted, user_id);

-- changeset liquibase:1733295843967-69
CREATE INDEX idx_dmp_oid_dname ON rv_dmp(org_id, dmp_name);

-- changeset liquibase:1733295843967-70
CREATE INDEX idx_dmp_oid_uid ON rv_dmp_user(org_id, dmp_id, user_id);

-- changeset liquibase:1733295843967-71
CREATE INDEX idx_dmpp_did_uid ON rv_dmp_perm(dmp_id, user_id);

-- changeset liquibase:1733295843967-72
CREATE INDEX idx_dmpp_oid_uid ON rv_dmp_perm(org_id, dmp_id, user_id);

-- changeset liquibase:1733295843967-73
CREATE INDEX idx_dtj_oid_tid_ct ON rv_dmp_task_job(org_id, task_id, deleted, create_time);

-- changeset liquibase:1733295843967-74
CREATE INDEX idx_dutr_oid_tid_uid ON rv_dmp_user_task_result(org_id, task_id, user_id);

-- changeset liquibase:1733295843967-75
CREATE INDEX idx_oid_did_tid ON rv_dmp_task_dim(org_id, dmp_id, task_id);

-- changeset liquibase:1733295843967-76
CREATE INDEX idx_oid_did_tid_uid ON rv_dmp_user_dim_detail(org_id, dmp_id, task_id, user_id, dim_id);

-- changeset liquibase:1733295843967-77
CREATE INDEX idx_oid_did_tid_uid ON rv_dmp_user_dim_result(org_id, dmp_id, task_id, user_id, dim_id);

-- changeset liquibase:1733295843967-78
CREATE INDEX idx_oid_did_uid ON rv_dmp_user_result(org_id, dmp_id, user_id);

-- changeset liquibase:1733295843967-79
CREATE INDEX idx_oid_did_uid_tid ON rv_dmp_user_dim_detail(org_id, dmp_id, task_id, dim_id, user_id);

-- changeset liquibase:1733295843967-80
CREATE INDEX idx_oid_did_uid_tid ON rv_dmp_user_dim_result(org_id, dmp_id, task_id, dim_id, user_id);

-- changeset liquibase:1733295843967-81
CREATE INDEX idx_oid_dmpid ON rv_dmp_user_auto_group(org_id, dmp_id);

-- changeset liquibase:1733295843967-82
CREATE INDEX idx_orgId ON rv_performance(org_id);

-- changeset liquibase:1733295843967-83
CREATE INDEX idx_orgId ON rv_performance_period(org_id);

-- changeset liquibase:1733295843967-84
CREATE INDEX idx_orgId_categoryId ON rv_project(org_id, project_category_id);

-- changeset liquibase:1733295843967-85
CREATE INDEX idx_orgId_configId ON rv_dimension_config_tool(org_id, project_id, dimension_config_id);

-- changeset liquibase:1733295843967-86
CREATE INDEX idx_orgId_donfigId ON rv_prj_rule(org_id, dimension_config_id);

-- changeset liquibase:1733295843967-87
CREATE INDEX idx_orgId_meetId_userId ON rv_meeting_user_remark(org_id, meeting_id, user_id);

-- changeset liquibase:1733295843967-88
CREATE INDEX idx_orgId_meetingId_userId ON rv_calibration_meeting_user(org_id, meeting_id, user_id);

-- changeset liquibase:1733295843967-89
CREATE INDEX idx_orgId_name ON rv_category(org_id, category_name);

-- changeset liquibase:1733295843967-90
CREATE INDEX idx_orgId_name ON rv_dimension(org_id, dimension_name);

-- changeset liquibase:1733295843967-91
CREATE INDEX idx_orgId_order ON rv_prj_result_config(org_id, order_index);

-- changeset liquibase:1733295843967-92
CREATE INDEX idx_orgId_prjId_configId_dimenId_userId ON rv_prj_user_calc_import(org_id, project_id, dimension_config_id, dimension_id, user_id);

-- changeset liquibase:1733295843967-93
CREATE INDEX idx_orgId_prjId_dimId ON rv_rule_condition(org_id, project_id, dimension_id, rule_id);

-- changeset liquibase:1733295843967-94
CREATE INDEX idx_orgId_prjId_dimId_userId ON rv_prj_user_calc_result(org_id, project_id, dimension_id, user_id);

-- changeset liquibase:1733295843967-95
CREATE INDEX idx_orgId_prjId_userId ON rv_prj_manager(org_id, project_id, user_id);

-- changeset liquibase:1733295843967-96
CREATE INDEX idx_orgId_prjId_userId ON rv_prj_user(org_id, project_id, user_id);

-- changeset liquibase:1733295843967-97
CREATE INDEX idx_orgId_prjName ON rv_project(org_id, project_name);

-- changeset liquibase:1733295843967-98
CREATE INDEX idx_orgId_prjRuleId ON rv_prj_rule_expression(org_id, prj_rule_id);

-- changeset liquibase:1733295843967-99
CREATE INDEX idx_orgId_projId_meetId_userId ON rv_meeting_user(org_id, project_id, meeting_id, user_id);

-- changeset liquibase:1733295843967-100
CREATE INDEX idx_orgId_projId_meetId_userId ON rv_meeting_user_remark(org_id, project_id, meeting_id, user_id);

-- changeset liquibase:1733295843967-101
CREATE INDEX idx_orgId_projId_modelId ON rv_dimension_model_same(org_id, project_id, model_id);

-- changeset liquibase:1733295843967-102
CREATE INDEX idx_orgId_projectId ON rv_calibration_meeting(org_id, project_id);

-- changeset liquibase:1733295843967-103
CREATE INDEX idx_orgId_projectId ON rv_project_training(org_id, project_id);

-- changeset liquibase:1733295843967-104
CREATE INDEX idx_orgId_projectId_dimenId ON rv_dimension_config(org_id, project_id, dimension_id);

-- changeset liquibase:1733295843967-105
CREATE INDEX idx_orgId_projectId_meetingId_dinenId_userId ON rv_calibration_meeting_result(org_id, project_id, meeting_id, dimension_id, user_id);

-- changeset liquibase:1733295843967-106
CREATE INDEX idx_orgId_rcid ON rv_prj_result_rule(org_id, result_config_id);

-- changeset liquibase:1733295843967-107
CREATE INDEX idx_orgId_trainingId ON rv_project_training(org_id, training_id);

-- changeset liquibase:1733295843967-108
CREATE INDEX idx_org_dmp ON rv_dmp_pos(org_id, dmp_id);

-- changeset liquibase:1733295843967-109
CREATE INDEX idx_org_id_third_user_id ON rv_career_history(org_id, third_user_id);

-- changeset liquibase:1733295843967-110
CREATE INDEX idx_org_user ON rv_ai_tool_session(org_id, user_id);

-- changeset liquibase:1733295843967-111
CREATE INDEX idx_org_user_id ON rv_user_data(org_id, user_id);

-- changeset liquibase:1733295843967-112
CREATE INDEX idx_orgid_meetingid_userid ON rv_calibration_meeting_result(org_id, meeting_id, user_id);

-- changeset liquibase:1733295843967-113
CREATE INDEX idx_performance_grade_orgId ON rv_performance_grade(org_id);

-- changeset liquibase:1733295843967-114
CREATE INDEX idx_pl_org_id ON rv_project_label(org_id);

-- changeset liquibase:1733295843967-115
CREATE INDEX idx_ppm_name ON rv_clound_project_eval(eval_name);

-- changeset liquibase:1733295843967-116
CREATE INDEX idx_ppm_project_detail_id ON rv_clound_project_eval(projet_detail_id);

-- changeset liquibase:1733295843967-117
CREATE INDEX idx_ppm_project_id ON rv_clound_project_detail(clound_project_id);

-- changeset liquibase:1733295843967-118
CREATE INDEX idx_rdc_did_oid ON rv_dmp_condition(dim_id, org_id);

-- changeset liquibase:1733295843967-119
CREATE INDEX idx_rdrl_oid_did_sid ON rv_dmp_rule_score_detail(org_id, dmp_id, score_id);

-- changeset liquibase:1733295843967-120
CREATE INDEX idx_rp_name ON rv_clound_project(org_id, project_id);

-- changeset liquibase:1733295843967-121
CREATE INDEX idx_ruf_outt ON rv_user_focus(org_id, user_id, target_id, target_type);

-- changeset liquibase:1733295843967-122
CREATE INDEX idx_session ON rv_ai_tool_session(session_id);

-- changeset liquibase:1733295843967-123
CREATE INDEX idx_session_id ON rv_ai_tool_message(session_id);

-- changeset liquibase:1733295843967-124
CREATE INDEX idx_setting_orgId ON rv_org_setting(org_id);

-- changeset liquibase:1733295843967-125
CREATE INDEX idx_start_time ON rv_ai_tool_session(start_time);

-- changeset liquibase:1733295843967-126
CREATE INDEX idx_third_user_id_org_id ON rv_reward_punishment_history(third_user_id, org_id);

-- changeset liquibase:1733295843967-127
CREATE INDEX idx_ue_tuid_oid ON rv_user_ext(third_user_id, org_id);

-- changeset liquibase:1733295843967-128
CREATE INDEX idx_uid_oid ON rv_ai_tool_message(user_id, org_id);

-- changeset liquibase:1733295843967-129
CREATE INDEX uk_orgid_gragdevalue ON rv_performance_grade(org_id, grade_value);

