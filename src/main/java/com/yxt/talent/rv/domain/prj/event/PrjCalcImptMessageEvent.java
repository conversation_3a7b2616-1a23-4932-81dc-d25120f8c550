package com.yxt.talent.rv.domain.prj.event;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@Setter
@Getter
@SuperBuilder
@NoArgsConstructor
@ToString(callSuper = true)
public class PrjCalcImptMessageEvent extends AbstractPrjCalcEvent {

    // 是否持久化
    private boolean persistence;

    private String prjDimId;

}
