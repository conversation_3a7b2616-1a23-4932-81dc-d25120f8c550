package com.yxt.talent.rv.domain.pos;

import com.yxt.AggregateRoot;
import com.yxt.common.util.Validate;
import com.yxt.talent.rv.domain.pos.entity.UdpPos;
import jakarta.annotation.Nonnull;
import lombok.*;
import org.springframework.data.annotation.ReadOnlyProperty;

@Getter
@Setter
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true, of = {"orgId"})
public class Pos extends AggregateRoot<String> {

    @Nonnull
    private String orgId;

    @Nonnull
    @ReadOnlyProperty
    private UdpPos udpPos;

    public Pos(@NonNull UdpPos udpPos) {
        this.udpPos = udpPos;
        Validate.isNotNull(udpPos.getId(), "id must not be null");
        Validate.isNotNull(udpPos.getOrgId(), "orgId must not be null");
        this.setId(udpPos.getId());
        this.orgId = udpPos.getOrgId();
    }

}
