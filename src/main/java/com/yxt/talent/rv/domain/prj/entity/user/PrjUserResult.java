package com.yxt.talent.rv.domain.prj.entity.user;


import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yxt.AuditDomainEntity;
import jakarta.annotation.Nonnull;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户盘点校准结果表
 */
@Setter
@Getter
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true, of = {"orgId"})
public class PrjUserResult extends AuditDomainEntity<String, LocalDateTime> {

    // 机构id
    @Nonnull
    private String orgId;

    // 盘点项目id
    @Nonnull
    private String prjId;

    // 维度id
    @Nonnull
    private String dimId;

    // 用户id
    @Nonnull
    private String userId;

    // 原始结果（0-默认，1-低，2-中，3-高）
    private Integer initLevel;
    @JsonIgnore
    @JSONField(serialize = false)
    private transient Level initLevelEnum;

    // 原始分数
    private BigDecimal initScore;

    // 校准结果（0-默认，1-低，2-中，3-高）
    private Integer lastLevel;
    @JsonIgnore
    @JSONField(serialize = false)
    private transient Level lastLevelEnum;

    // 校准分数
    private BigDecimal lastScore;

    @Getter
    @RequiredArgsConstructor
    public enum Level {
        /**
         * 盘点结果等级
         */
        HIGH("高", 3),
        MIDDLE("中", 2),
        LOW("低", 1),
        DEFAULT("", 0);

        private final String desc;
        private final int code;

        public static String getNameByCode(Integer code) {
            if (code == null) {
                return DEFAULT.desc;
            }
            for (Level level : Level.values()) {
                if (level.getCode() == code) {
                    return level.getDesc();
                }
            }
            return DEFAULT.desc;
        }

        public static int getCodeByName(String name) {
            for (Level value : Level.values()) {
                if (value.getDesc().equals(name)) {
                    return value.getCode();
                }
            }
            return DEFAULT.code;
        }

        public static boolean isNameExist(String name) {
            for (Level value : Level.values()) {
                if (value.getDesc().equals(name)) {
                    return true;
                }
            }
            return false;
        }

        public static Map<Integer, String> getCodeNameMap() {
            Map<Integer, String> tempMap = new HashMap<>(8);
            for (Level e : values()) {
                tempMap.put(e.code, e.desc);
            }
            return tempMap;
        }

        public static Map<String, Integer> getNameCodeMap() {
            Map<String, Integer> tempMap = new HashMap<>(8);
            for (Level e : values()) {
                tempMap.put(e.desc, e.code);
            }
            return tempMap;
        }
    }

}
