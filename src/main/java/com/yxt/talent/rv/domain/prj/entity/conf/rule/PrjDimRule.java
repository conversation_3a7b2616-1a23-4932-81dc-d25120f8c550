package com.yxt.talent.rv.domain.prj.entity.conf.rule;


import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.yxt.AuditDomainEntity;
import com.yxt.util.NonRemovableLinkedHashSet;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Set;

/**
 * 盘点计算规则表
 */
@Slf4j
@Setter
@Getter
@NoArgsConstructor
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true, of = {"orgId"})
public class PrjDimRule extends AuditDomainEntity<String, LocalDateTime> {

    // 机构id
    @Nonnull
    private String orgId;

    // 维度配置关系主键id
    private String dimConfId;

    // 规则计算优先级，默认高等级优先，0-高等级优先， 1-低等级优先
    private Integer computePriority = 0;

    // 规则说明
    private String ruleRemark;

    // 分级类型（0-枚举，1-百分比，2-绝对值）
    private Integer ruleType;
    @JsonIgnore
    @JSONField(serialize = false)
    private transient RuleType ruleTypeEnum;

    public void setRuleType(Integer ruleType) {
        this.ruleType = ruleType;
        this.ruleTypeEnum = RuleType.byCode(ruleType);
    }

    @Nonnull
    @Setter(AccessLevel.NONE)
    private final Set<PrjDimRuleCond> prjDimRuleCondSet = new NonRemovableLinkedHashSet<>();

    @Nonnull
    @Setter(AccessLevel.NONE)
    private final Set<PrjDimRuleExpr> prjDimRuleExprSet = new NonRemovableLinkedHashSet<>();

    public void addPrjDimRuleCond(PrjDimRuleCond entity) {
        if (entity == null) {
            log.warn("LOG10485:{}", this.dimConfId);
            return;
        }
        prjDimRuleCondSet.add(entity);
    }

    public void addPrjDimRuleCond(Collection<PrjDimRuleCond> entities) {
        if (entities == null || entities.isEmpty()) {
            log.warn("LOG10495:{}", this.dimConfId);
            return;
        }
        prjDimRuleCondSet.addAll(entities);
    }

    public void addPrjDimRuleExpr(PrjDimRuleExpr entity) {
        if (entity == null) {
            log.warn("LOG11805:{}", this.dimConfId);
            return;
        }
        prjDimRuleExprSet.add(entity);
    }

    public void addPrjDimRuleExpr(Collection<PrjDimRuleExpr> entities) {
        if (entities == null || entities.isEmpty()) {
            log.warn("LOG11815:{}", this.dimConfId);
            return;
        }
        prjDimRuleExprSet.addAll(entities);
    }

    @Getter
    @RequiredArgsConstructor
    public enum RuleType {
        /**
         * 分级类型
         */
        UNKNOWN(-1, "未知"),
        ENUMERATION(0, "枚举"),
        PERCENTAGE(1, "百分比"),
        ABSOLUTE(2, "绝对值"),
        BEI_ZHI_EVAL(3, "倍智测评");
        private final int code;
        private final String desc;

        public static RuleType byCode(@Nullable Integer code) {
            if (code == null) {
                return UNKNOWN;
            }
            for (RuleType ruleType : RuleType.values()) {
                if (ruleType.code == code) {
                    return ruleType;
                }
            }
            return UNKNOWN;
        }
    }
}
