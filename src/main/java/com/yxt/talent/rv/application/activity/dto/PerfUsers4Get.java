package com.yxt.talent.rv.application.activity.dto;
import java.lang.String;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Date;
import com.yxt.modelhub.api.bean.dto.AmUser4DTO;
import com.yxt.modelhub.api.bean.dto.AmSlDrawer4RespDTO;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
@Getter
@Setter
@Schema(description = "绩效评估人员详情返回")
public class PerfUsers4Get implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description="主键;id主键")
    private String id;
    @Schema(description="机构号;机构id")
    private String orgId;
    @Schema(description="名称")
    private String name;
    @Schema(description="父实体id;子实体选择的父实体id或者引用对象的实体id")
    private String rvPerfAssessmentId;
    @Schema(description="父实体id展示信息")
    @JsonProperty("@rvPerfAssessmentId")
    private Rv_perf_assessment4Get rvPerfAssessmentId__Record;
    @Schema(description="创建时间")
    private Date createTime;
    @Schema(description="更新时间")
    private Date updateTime;
    @Schema(description="创建人;创建人id")
    @JsonProperty("@createUserId")
    private AmUser4DTO createUserId;
    @Schema(description="更新人;更新人id")
    @JsonProperty("@updateUserId")
    private AmUser4DTO updateUserId;
    @Schema(description="评估员工;按照业务需求,返回应用的实体字段")
    @JsonProperty("@userid")
    private AmUser4DTO userid;
    @Schema(description="完成状态")
    private String status;
    @Schema(description="评估结果;按照业务需求,返回应用的实体字段")
    @JsonProperty("@resultconfid")
    private AmSlDrawer4RespDTO resultconfid;
    @Schema(description="是否达标")
    private String qualified;
    @Schema(description="得分")
    private BigDecimal resultscore;
    @Schema(description="状态")
    private String numberstate;
}
