package com.yxt.talent.rv.application.xpd.result.calculator;

import com.yxt.talent.rv.application.xpd.common.dto.CaliDimResultDto;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdGridCellMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdGridLevelMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdGridMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.*;
import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 用户维度组结果计算器
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserDimCombResultCalculator {

    private final XpdGridMapper xpdGridMapper;
    private final XpdGridLevelMapper xpdGridLevelMapper;
    private final XpdGridCellMapper xpdGridCellMapper;

    public List<XpdResultUserDimcombPO> calculateUserDimCombResults(
            String orgId, String xpdId, String userId, List<XpdDimCombPO> dimCombs,
            Map<String, CaliDimResultDto> dimResultMap, String operatorId) {

        List<XpdResultUserDimcombPO> resultList = new ArrayList<>();

        XpdGridPO xpdGrid = getXpdGrid(orgId, xpdId);
        if (xpdGrid == null) {
            log.warn("LOG21683:项目{}没有宫格配置，无法计算维度组结果", xpdId);
            return resultList;
        }

        List<XpdGridLevelPO> gridLevels = xpdGridLevelMapper.listByGridId(orgId, xpdGrid.getId());
        Map<String, Integer> gridLevelOrderMap = gridLevels.stream()
            .collect(Collectors.toMap(XpdGridLevelPO::getId, XpdGridLevelPO::getOrderIndex));

        List<XpdGridCellPO> gridCells = xpdGridCellMapper.listByGridId(orgId, xpdGrid.getId());
        Map<String, XpdGridCellPO> gridCellMap = buildGridCellMap(gridCells, xpdGrid.getConfigType());

        for (XpdDimCombPO dimComb : dimCombs) {
            XpdResultUserDimcombPO combResult = calculateSingleDimCombResult(
                orgId, xpdId, userId, dimComb, dimResultMap, gridLevelOrderMap,
                gridCellMap, xpdGrid.getConfigType(), operatorId);

            if (combResult != null) {
                resultList.add(combResult);
            }
        }
        return resultList;
    }

    @Nullable
    private XpdGridPO getXpdGrid(String orgId, String xpdId) {
        return xpdGridMapper.selectByXpdId(orgId, xpdId);
    }

    private Map<String, XpdGridCellPO> buildGridCellMap(List<XpdGridCellPO> gridCells, Integer configType) {
        return gridCells.stream().collect(Collectors.toMap(
            cell -> buildCellKey(cell.getXIndex(), cell.getYIndex(), cell.getDimCombId(), configType),
            Function.identity()
        ));
    }

    private String buildCellKey(Integer xIndex, Integer yIndex, String dimCombId, Integer configType) {
        return (configType != null && configType == 0) ?
            xIndex + "_" + yIndex :
            dimCombId + "_" + xIndex + "_" + yIndex;
    }

    @Nullable
    private XpdResultUserDimcombPO calculateSingleDimCombResult(
            String orgId, String xpdId, String userId, XpdDimCombPO dimComb,
            Map<String, CaliDimResultDto> dimResultMap, Map<String, Integer> gridLevelOrderMap,
            Map<String, XpdGridCellPO> gridCellMap, Integer configType, String operatorId) {

        CaliDimResultDto xDimResult = dimResultMap.get(dimComb.getXSdDimId());
        CaliDimResultDto yDimResult = dimResultMap.get(dimComb.getYSdDimId());

        if (xDimResult == null || yDimResult == null) {
            log.debug("LOG21693:用户{}在维度组{}中缺少维度结果，X轴维度{}结果:{}, Y轴维度{}结果:{}",
                userId, dimComb.getId(), dimComb.getXSdDimId(), xDimResult != null,
                dimComb.getYSdDimId(), yDimResult != null);
            return null;
        }

        Integer xIndex = gridLevelOrderMap.get(xDimResult.getGridLevelId());
        Integer yIndex = gridLevelOrderMap.get(yDimResult.getGridLevelId());

        if (xIndex == null || yIndex == null) {
            log.warn("LOG21703:用户{}在维度组{}中无法获取坐标，X轴分层{}坐标:{}, Y轴分层{}坐标:{}",
                userId, dimComb.getId(), xDimResult.getGridLevelId(), xIndex,
                yDimResult.getGridLevelId(), yIndex);
            return null;
        }

        String cellKey = buildCellKey(xIndex, yIndex, dimComb.getId(), configType);
        XpdGridCellPO gridCell = gridCellMap.get(cellKey);

        if (gridCell == null) {
            log.warn("LOG21713:用户{}在维度组{}中无法找到格子，坐标({},{})，key:{}",
                userId, dimComb.getId(), xIndex, yIndex, cellKey);
            return null;
        }

        XpdResultUserDimcombPO result = new XpdResultUserDimcombPO();
        result.setOrgId(orgId);
        result.setXpdId(xpdId);
        result.setUserId(userId);
        result.setDimCombId(dimComb.getId());
        result.setCellId(gridCell.getId());
        EntityUtil.setAuditFields(result, operatorId);

        // 注意：之前在DefaultUserDimCombResultStrategy中，PO的id, cellIndex, deleted, caliFlag, createTime, updateTime等字段未在此处设置
        // 请根据您的业务需求确认这些字段是否需要在此处填充，或者它们是由调用方、数据库默认值或MyBatis Plus的填充策略处理的。
        // 例如，ID通常由数据库或MP自动生成，时间戳可由MP自动填充。

        return result;
    }
}
