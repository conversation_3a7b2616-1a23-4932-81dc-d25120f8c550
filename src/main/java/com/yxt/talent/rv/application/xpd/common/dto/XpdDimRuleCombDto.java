package com.yxt.talent.rv.application.xpd.common.dto;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimRuleCalcPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdDimRulePO;
import lombok.Builder;
import lombok.Data;
import org.assertj.core.util.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/10 16:29
 */
@Data
@Builder
public class XpdDimRuleCombDto {

    private XpdDimRulePO upDimRule;

    @Builder.Default()
    private List<XpdDimRulePO> newDimRuleList = Lists.newArrayList();

    @Builder.Default()
    private List<XpdDimRulePO> upDimRuleList = Lists.newArrayList();

    @Builder.Default()
    private List<String> delDimRuleIdList = Lists.newArrayList();

    @Builder.Default()
    private List<XpdDimRuleCalcPO> newDimRuleCalcList = Lists.newArrayList();

    @Builder.Default()
    private List<String> delDimRuleCalcIdList = Lists.newArrayList();

    private String delDimRuleId;
}
