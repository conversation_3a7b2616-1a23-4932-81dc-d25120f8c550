package com.yxt.talent.rv.application.user;

import com.yxt.ApplicationCommandService;
import com.yxt.common.Constants;
import com.yxt.common.util.StreamUtil;
import com.yxt.event.EventPublisher;
import com.yxt.talent.rv.controller.openapi.command.CareerHistorySyncOpenCmd;
import com.yxt.talent.rv.controller.openapi.command.RewardPunishmentHistorySyncOpenCmd;
import com.yxt.talent.rv.controller.openapi.command.UserExtSyncOpenCmd;
import com.yxt.talent.rv.domain.user.event.UserProductCodeChangeDomainEvent;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.career.CareerHistoryMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.reward.RewardPunishmentHistoryMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.user.UserExtMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.user.CareerHistoryPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.user.RewardPunishmentHistoryPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.user.UserExtPO;
import com.yxt.udpfacade.bean.user.User4Mq;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BinaryOperator;

import static com.yxt.talent.rv.domain.user.event.UserProductCodeChangeDomainEvent.ActionEnum.ADD;
import static com.yxt.talent.rv.domain.user.event.UserProductCodeChangeDomainEvent.ActionEnum.DELETE;

@RequiredArgsConstructor
@ApplicationCommandService
public class UserCmdAppService {

    private static final Logger log = LoggerFactory.getLogger(UserCmdAppService.class);
    private final EventPublisher eventPublisher;
    private final UserExtMapper userExtMapper;
    private final UdpLiteUserMapper udpLiteUserMapper;
    private final RewardPunishmentHistoryMapper rewardPunishmentHistoryMapper;
    private final CareerHistoryMapper careerHistoryMapper;

    /**
     * 当用户信息变更时，触发该方法
     *
     * @param user
     */
    public void dealWhenUserUpdate(User4Mq user) {
        if (user == null ||
            (StringUtils.isBlank(user.getId()) && StringUtils.isBlank(user.getOrgId()))) {
            log.warn("LOG12545:");
            return;
        }

        String orgId = user.getOrgId();
        String userId = user.getId();

        if (StringUtils.isBlank(orgId) || StringUtils.isBlank(userId)) {
            log.warn("LOG13485:orgId={}, userId={}", orgId, userId);
            return;
        }

        // 如果用户被移除了奇点的产品标识, 需要发送用户表示变更的领域事件，由其他关心该事件的领域自行处理
        String oldProductCodes = user.getBefore().getProductCodeList();
        String newProductCodes = user.getProductCodeList();
        log.debug("LOG13375:orgId={}, username={}, newProductCodes={}, oldProductCodes={}", orgId,
                user.getUsername(), newProductCodes, oldProductCodes);


        BinaryOperator<Boolean> deleteSupplier =
                (oldContainsSp, newContainsSp) -> oldContainsSp && !newContainsSp;
        BinaryOperator<Boolean> addSupplier =
                (oldContainsSp, newContainsSp) -> !oldContainsSp && newContainsSp;

        if (isSpProductCodeChange(oldProductCodes, newProductCodes, deleteSupplier)) {
            eventPublisher.publishAsync(
                    new UserProductCodeChangeDomainEvent(orgId, userId, DELETE));
        } else if (isSpProductCodeChange(oldProductCodes, newProductCodes, addSupplier)) {
            eventPublisher.publishAsync(new UserProductCodeChangeDomainEvent(orgId, userId, ADD));
        }
    }

    /**
     * 检测是否删除了奇点产品标识
     *
     * @param oldProductCodes
     * @param newProductCodes
     * @return
     */
    private static boolean isSpProductCodeChange(
            String oldProductCodes, String newProductCodes, BinaryOperator<Boolean> supplier) {
        boolean oldContainsSp =
                oldProductCodes != null && oldProductCodes.contains(Constants.SP_PRODUCT_CODE);

        boolean newContainsSp =
                newProductCodes != null && newProductCodes.contains(Constants.SP_PRODUCT_CODE);

        return supplier.apply(oldContainsSp, newContainsSp);
    }

    @Transactional(rollbackFor = Exception.class)
    public void syncUserExt(String orgId, Collection<UserExtSyncOpenCmd> datas) {
        List<String> thirdUserIds = StreamUtil.mapList(datas, UserExtSyncOpenCmd::getThirdUserId);

        // 保存三方id和二方id的映射关系 ==> key: thirdUserId, value: udpId
        List<UdpLiteUserPO> udpUsers = udpLiteUserMapper.selectByThirdUserIds(orgId, thirdUserIds);
        Map<String, String> userIdDict =
                StreamUtil.list2map(udpUsers, UdpLiteUserPO::getThirdUserId, UdpLiteUserPO::getId);
        List<String> userIds = udpUsers.stream().map(UdpLiteUserPO::getId).toList();
        Map<String, String> userExtMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(userIds)) {
            List<UserExtPO> userExtPOS = userExtMapper.selectByAndUserIds(orgId, userIds);
            userExtMap = StreamUtil.list2map(userExtPOS, UserExtPO::getUserId, UserExtPO::getId);
        }

        Collection<UserExtPO> converted =
                UserExtSyncOpenCmd.Assembler.INSTANCE.convert(orgId, userIdDict, datas, userExtMap);
        userExtMapper.insertOrUpdateBatch(converted);
    }

    @Transactional(rollbackFor = Exception.class)
    public void syncEph(String orgId, Collection<RewardPunishmentHistorySyncOpenCmd> datas) {
        Collection<RewardPunishmentHistoryPO> converted =
                RewardPunishmentHistorySyncOpenCmd.Assembler.INSTANCE.convert(orgId, datas);
        // 添加userId
        if (CollectionUtils.isNotEmpty(converted)) {
            List<String> thirdUserIds = converted.stream().map(RewardPunishmentHistoryPO::getThirdUserId).toList();
            List<UdpLiteUserPO> udpLiteUserList = udpLiteUserMapper.selectByThirdUserIdsAll(orgId, thirdUserIds);
            Map<String, String> userMap =
                StreamUtil.list2map(udpLiteUserList, UdpLiteUserPO::getThirdUserId, UdpLiteUserPO::getId);
            for (RewardPunishmentHistoryPO rewardPunishmentHistory : converted) {
                String thirdUserId = rewardPunishmentHistory.getThirdUserId();
                String userId = userMap.get(thirdUserId);
                if (userId != null) {
                    rewardPunishmentHistory.setUserId(userId);
                }
            }
        }
        rewardPunishmentHistoryMapper.insertOrUpdateBatch(converted);
    }

    @Transactional(rollbackFor = Exception.class)
    public void syncCh(String orgId, Collection<CareerHistorySyncOpenCmd> datas) {
        Collection<CareerHistoryPO> converted =
                CareerHistorySyncOpenCmd.Assembler.INSTANCE.convert(orgId, datas);
        if (CollectionUtils.isNotEmpty(converted)) {
            List<String> thirdUserIds = converted.stream().map(CareerHistoryPO::getThirdUserId).toList();
            List<UdpLiteUserPO> udpLiteUserList = udpLiteUserMapper.selectByThirdUserIdsAll(orgId, thirdUserIds);
            Map<String, String> userMap =
                StreamUtil.list2map(udpLiteUserList, UdpLiteUserPO::getThirdUserId, UdpLiteUserPO::getId);
            for (CareerHistoryPO careerHistory : converted) {
                String thirdUserId = careerHistory.getThirdUserId();
                String userId = userMap.get(thirdUserId);
                if (userId != null) {
                    careerHistory.setUserId(userId);
                }
            }
        }
        careerHistoryMapper.insertOrUpdateBatch(converted);
    }
}
