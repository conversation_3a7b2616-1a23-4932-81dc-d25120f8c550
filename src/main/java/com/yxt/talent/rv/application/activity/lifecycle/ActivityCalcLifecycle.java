package com.yxt.talent.rv.application.activity.lifecycle;

import com.yxt.talent.rv.infrastructure.service.taskprogress.core.StateTransitionRule;
import com.yxt.talent.rv.infrastructure.service.taskprogress.core.TaskLifecycle;
import com.yxt.talent.rv.infrastructure.service.taskprogress.core.TaskState;
import com.yxt.talent.rv.infrastructure.service.taskprogress.core.TaskStateFactory;
import org.springframework.stereotype.Component;

import java.util.Objects;

import static com.yxt.talent.rv.application.activity.lifecycle.ActivityStateFactory.CALCULATING;
import static com.yxt.talent.rv.application.activity.lifecycle.ActivityStateFactory.COMPLETED;
import static com.yxt.talent.rv.application.activity.lifecycle.ActivityStateFactory.getInstance;

/**
 * 活动计算生命周期
 * 提供了基本的状态定义和转换规则
 */
public abstract class ActivityCalcLifecycle implements TaskLifecycle {

    @Override
    public TaskStateFactory getStateFactory() {
        return getInstance();
    }

    @Override
    public StateTransitionRule getTransitionRule() {
        return new ActivityTransitionRule();
    }

    /**
     * 活动状态转换规则
     * 定义了状态之间的转换规则
     */
    protected static class ActivityTransitionRule implements StateTransitionRule {
        @Override
        public boolean canTransit(TaskState from, TaskState to) {
            // 从计算中转换到已完成 && 状态未变化
            String fromCode = from.getCode();
            String toCode = to.getCode();
            return (fromCode.equals(CALCULATING.getCode()) && toCode.equals(COMPLETED.getCode())) ||
                   Objects.equals(fromCode, toCode);
        }
    }

    /**
     * 绩效活动生命周期实现
     */
    @Component
    public static class PerfActivityLifecycle extends ActivityCalcLifecycle {
        public static final String TYPE = "ACTV_PERF";

        @Override
        public String getType() {
            return TYPE;
        }
    }

    /**
     * 人才档案活动生命周期实现
     */
    @Component
    public static class ProfActivityLifecycle extends ActivityCalcLifecycle {
        public static final String TYPE = "ACTV_PROF";

        @Override
        public String getType() {
            return TYPE;
        }
    }
}
