package com.yxt.talent.rv.application.xpd.result.strategy.userdimresult;

import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdUserDimResultVO;
import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdUserDimResultsVO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetResultUserDimMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetResultUserDimPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridPO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 从校准会表填充用户维度结果的策略实现
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CalimeetUserDimResultFillStrategy implements UserDimResultFillStrategy {

    private final CalimeetResultUserDimMapper calimeetResultUserDimMapper;

    @Override
    public void fillUserDimResults(String orgId, String xpdId, List<String> userIds,
                                  XpdGridPO xpdGrid, Map<String, XpdUserDimResultsVO> userInfoMap) {
        // 注意：对于校准会策略，xpdId 参数实际上是 calimeetId
        String calimeetId = xpdId;
        
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }

        // 从校准会表获取用户维度结果
        List<CalimeetResultUserDimPO> resultUserDims =
            calimeetResultUserDimMapper.selectByCaliMeetIdAndUserIds(orgId, calimeetId, userIds);

        if (CollectionUtils.isEmpty(resultUserDims)) {
            return;
        }

        // 按用户ID分组维度数据
        Map<String, List<CalimeetResultUserDimPO>> userDimMap =
            resultUserDims.stream().collect(Collectors.groupingBy(CalimeetResultUserDimPO::getUserId));

        // 为每个用户填充维度结果
        userDimMap.forEach((userId, calimeetResultUserDims) -> {
            XpdUserDimResultsVO userInfo = userInfoMap.get(userId);
            if (userInfo != null && CollectionUtils.isNotEmpty(calimeetResultUserDims)) {
                List<XpdUserDimResultVO> vos = calimeetResultUserDims.stream()
                    .map(this::convertToXpdUserDimResultVO)
                    .collect(Collectors.toList());
                userInfo.setUserDimResults(vos);
            }
        });
    }

    @Override
    public UserDimResultFillStrategyType getStrategyType() {
        return UserDimResultFillStrategyType.CALIMEET;
    }

    /**
     * 将校准会用户维度结果转换为标准的用户维度结果VO
     *
     * @param calimeetResultUserDimPO 校准会用户维度结果PO
     * @return 标准的用户维度结果VO
     */
    private XpdUserDimResultVO convertToXpdUserDimResultVO(CalimeetResultUserDimPO calimeetResultUserDimPO) {
        XpdUserDimResultVO vo = new XpdUserDimResultVO();
        vo.setSdDimId(calimeetResultUserDimPO.getSdDimId());
        vo.setGridLevelId(calimeetResultUserDimPO.getGridLevelId());
        vo.setGridLevelName(calimeetResultUserDimPO.getGridLevelName());
        vo.setGridLevelNameI18n(calimeetResultUserDimPO.getGridLevelNameI18n());
        vo.setGridLevelOrderIndex(calimeetResultUserDimPO.getGridLevelOrderIndex());
        vo.setThirdDimColor(calimeetResultUserDimPO.getThirdDimColor());
        vo.setScoreValue(calimeetResultUserDimPO.getScoreValue());
        vo.setQualifiedPtg(calimeetResultUserDimPO.getQualifiedPtg());
        vo.setPerfResultId(calimeetResultUserDimPO.getPerfResultId());
        return vo;
    }
}
