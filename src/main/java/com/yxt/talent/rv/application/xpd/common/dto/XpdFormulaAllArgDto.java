package com.yxt.talent.rv.application.xpd.common.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class XpdFormulaAllArgDto {
    private List<ArgRefRoot> refList = new ArrayList<>();

    @Data
    public static class ArgRefRoot {
        //参考 DimRuleCalcRefEnum
        @Schema(description = "0：活动ID/1：导入ID/2：个人档案")
        private int refType;
        private String refId;
        //ref UacdTypeEnum
        @Schema(description = "活动类型，actv_speval：测评")
        private String refRegId;
        private String refName;
        private List<ArgRefIndicator> refIndicators = new ArrayList<>();
    }

    @Data
    public static class ArgRefIndicator {
        private String sdIndicatorId;
        private String sdIndicatorName;
        private List<ArgRefIndicatorArg> chooseArgs = new ArrayList<>();
    }

    @Data
    public static class ArgRefIndicatorArg {
        private String argKey;
        private String name;
    }
}
