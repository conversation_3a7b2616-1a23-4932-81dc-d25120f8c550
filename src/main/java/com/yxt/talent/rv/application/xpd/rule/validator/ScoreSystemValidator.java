package com.yxt.talent.rv.application.xpd.rule.validator;

import com.yxt.talent.rv.application.xpd.common.dto.ErrorInfo;
import com.yxt.talent.rv.controller.manage.xpd.rule.enums.RuleErrorCodeEnum;
import com.yxt.talent.rv.controller.manage.xpd.rule.enums.ScoreSystemEnum;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.utilities.util.ScoreSystemUtil;
import jakarta.annotation.Nullable;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 分制校验工具类
 *
 * <AUTHOR>
 */
public class ScoreSystemValidator {

    private static final String SCORE_EXCEED_MAX_VALUE = "分值超过分制的最大值";

    /**
     * 校验分值是否超过分制的最大值
     *
     * @param score 分值
     * @param scoreSystem 分制
     * @return 错误信息，如果没有错误则返回null
     */
    @Nullable
    public static ErrorInfo validateScoreValue(BigDecimal score, Integer scoreSystem) {
        if (score == null || scoreSystem == null || scoreSystem == ScoreSystemEnum.ORIGINAL.getCode()) {
            return null;
        }

        if (ScoreSystemUtil.isScoreExceedMaxValue(score, scoreSystem)) {
            return new ErrorInfo(RuleErrorCodeEnum.WRONG.getCode(), ExceptionKeys.XPD_SCORE_EXCEED_MAX_VALUE, SCORE_EXCEED_MAX_VALUE);
        }
        
        return null;
    }

    /**
     * 校验分值列表是否超过分制的最大值
     *
     * @param scores 分值列表
     * @param scoreSystem 分制
     * @return 错误信息，如果没有错误则返回null
     */
    @Nullable
    public static ErrorInfo validateScoreValues(List<BigDecimal> scores, Integer scoreSystem) {
        if (scores == null || scores.isEmpty() || scoreSystem == null || scoreSystem == ScoreSystemEnum.ORIGINAL.getCode()) {
            return null;
        }

        for (BigDecimal score : scores) {
            if (score != null) {
                ErrorInfo errorInfo = validateScoreValue(score, scoreSystem);
                if (Objects.nonNull(errorInfo)) {
                    return errorInfo;
                }
            }
        }
        
        return null;
    }
}
