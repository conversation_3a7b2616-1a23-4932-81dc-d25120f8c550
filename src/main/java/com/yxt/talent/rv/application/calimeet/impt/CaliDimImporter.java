package com.yxt.talent.rv.application.calimeet.impt;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.util.StreamUtil;
import com.yxt.enums.DeleteEnum;
import com.yxt.export.I18nComponent;
import com.yxt.talent.rv.application.calimeet.CaliMeetImportService;
import com.yxt.talent.rv.application.calimeet.dto.CaliDimAndLevelImportResultVO;
import com.yxt.talent.rv.application.calimeet.dto.CaliDmDTO;
import com.yxt.talent.rv.application.calimeet.dto.CaliUserImportDTO;
import com.yxt.talent.rv.application.calimeet.dto.CaliUserImportDbDTO;
import com.yxt.talent.rv.application.xpd.common.dto.CaliUpdateUserResultReq;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.constant.RedisKeys;
import com.yxt.talent.rv.infrastructure.common.constant.enums.EnableEnum;
import com.yxt.talent.rv.infrastructure.common.transfer.FileProcessedResult;
import com.yxt.talent.rv.infrastructure.common.utilities.util.DateTimeUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdRuleConfPO;
import com.yxt.talent.rv.infrastructure.service.file.EasyExcelListener;
import com.yxt.talent.rv.infrastructure.service.file.FileConstants;
import com.yxt.talent.rv.infrastructure.service.file.command.FileImportCmd;
import com.yxt.talent.rv.infrastructure.service.file.impt.FileImportSupport;
import com.yxt.talent.rv.infrastructure.service.file.impt.FileImporter;
import com.yxt.talent.rv.infrastructure.service.file.impt.FileReader;
import jakarta.annotation.Nullable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;

import static org.apache.commons.lang3.StringUtils.EMPTY;

@Slf4j
@Component
@RequiredArgsConstructor
public class CaliDimImporter extends
    FileImporter<CaliUserImportDTO, CaliDimImporter.CaliImportProcessedResult, CaliDimAndLevelImportResultVO> {
    private static final String ERR_USERNAME_NOT_EXISTED="apis.sptalentrv.cali.dim.template.import.error.notexite";
    private static final String ERR_RESULT="apis.sptalentrv.cali.dim.template.import.error.resulterror";
    private static final String ERR_REASON_EMPTY="apis.sptalentrv.cali.dim.template.import.error.reason.empty";
    private static final String ERR_SCORE="apis.sptalentrv.cali.dim.template.import.error.score";
    private static final String ERR_MATCH="apis.sptalentrv.cali.dim.template.import.error.match";
    private static final String ERR_USERNAME_DISABLED = "apis.sptalentrv.cali.dim.template.import.error.disabled";
    private static final String ERR_USERNAME_NOT_IN_CALIS = "apis.sptalentrv.cali.dim.template.import.error.noticali";
    private static final String ERROR_EXPORT_FILE = "apis.sptalentrv.cali.dim.template.import.error.file.name";
    private static final String IMPORT_HEAD = "import:";
    private final CaliDimTemExportStrategy caliDimTemExportStrategy;
    private final UdpLiteUserMapper udpLiteUserMapper;
    private final I18nComponent i18nComponent;
    private final CalimeetUserMapper calimeetUserMapper;
    private final CaliMeetImportService caliMeetImportService;

    @Nullable
    public CaliDimAndLevelImportResultVO toImport(
        FileImportCmd bean, MultipartFile file, UserCacheDetail userCache) {

        String orgId = userCache.getOrgId();
        String operator = userCache.getUserId();
        String lockKey = String.format(RedisKeys.LK_CALI_DIM_IMPT, IMPORT_HEAD, orgId, operator);
        String errorFileName = i18nComponent.getI18nValue(ERROR_EXPORT_FILE) +
                               DateTimeUtil.dateToString(
                                   new Date(), DateTimeUtil.FORMATTER_YYYY_MM_DD_HH_MM_SS) +
                               FileConstants.FILE_SUFFIX_XLSX;

        Function<List<CaliUserImportDTO>, CaliImportProcessedResult> dataProcessor =
            importDataList -> dataProcess(importDataList, bean.getTargetId(), orgId, operator);

        FileImportSupport<CaliUserImportDTO, CaliImportProcessedResult, CaliDimAndLevelImportResultVO>
            fileImportSupport =
            FileImportSupport.<CaliUserImportDTO, CaliImportProcessedResult, CaliDimAndLevelImportResultVO>builder()
                .file(file)
                .fileId(bean.getFileId())
                .tranId(lockKey)
                .startRow(2)
                .orgId(orgId)
                .operator(operator)
                .importContentClazz(CaliUserImportDTO.class)
                .dataReader(new CaliDimImporter.CaliDimLevelFileReader(file, bean.getFileId()))
                .dataProcessor(dataProcessor)
                .outputStrategy(caliDimTemExportStrategy)
                .errorFileName(errorFileName)
                .resultProcessor(this::generateImportResult)
                .build();
        return toImport(fileImportSupport);
    }

    private CaliDimAndLevelImportResultVO generateImportResult(CaliImportProcessedResult processedResult) {
        int repeatCount = processedResult.getRepeatCount();
        int failedCount = processedResult.getFailedCount();
        int successCount = processedResult.getSuccessCount();
        int totalCount = processedResult.getTotalCount();
        List<CaliUserImportDTO> failedData = processedResult.getFailedData();
        Map<String, List<CaliUserImportDTO>> result = processedResult.getResult();
        String errorFilePath =
            Optional.ofNullable(processedResult.getErrorFilePath()).orElse(EMPTY);

        return CaliDimAndLevelImportResultVO.childBuilder()
            .repeatCount(repeatCount)
            .failedCount(failedCount)
            .successCount(successCount)
            .totalCount(totalCount)
            .filePath(errorFilePath)
            .successData(result)
            .failData(failedData)
            .build();
    }

    @NotNull
    private CaliImportProcessedResult dataProcess(
        List<CaliUserImportDTO> importDataList, String caliId, String orgId, String userId) {
        //6.3 获取盘点项目规则
        XpdRuleConfPO xpdRuleConfPO = caliMeetImportService.getXpdRule(orgId, caliId);

        List<CaliUserImportDTO> tempImportDataList = new ArrayList<>();

        // 处理换行
        for (CaliUserImportDTO pe : importDataList) {
            pe.setUserName(dealSpecialSymbol(pe.getUserName()));
            if (StringUtils.isEmpty(pe.getUserName())) {
                continue;
            }
            tempImportDataList.add(pe);
        }
        importDataList = tempImportDataList;

        // 校验导入的账号信息
        List<CaliUserImportDTO> userList =
            StreamUtil.filterList(importDataList, b -> StringUtils.isNotBlank(b.getUserName()));
        if (CollectionUtils.isEmpty(userList)) {
            throw new ApiException(ExceptionKeys.CALI_IMPORT_NO_USERNAME);
        }

        // 获取导入用户名信息
        List<UdpLiteUserPO> importUsers = getImportUsers(importDataList, orgId);
        log.debug("LOG10090:{}", importUsers.size());
        Map<String, UdpLiteUserPO> importUserMaps =
            StreamUtil.list2map(importUsers, UdpLiteUserPO::getUsername);

        // 导入失败数
        int errorCount = importDataList.size();
        // 导入重复数
        int repeatCount = 0;
        // 处理结果map，key为周期名称
        List<String> memberIds = getCaliUsers(orgId, caliId);

        // 获取校准会维度数据
        Map<String, CaliDmDTO> dimResultMap = getDimResult(orgId, xpdRuleConfPO.getXpdId());

        // 导入到db的数据
        List<CaliUserImportDbDTO> userResult = new ArrayList<>();

        Set<String> userIds = new HashSet<>();

        // 遍历导入的数据处理
        for (CaliUserImportDTO userImportDTO : importDataList) {
            // 账号检查
            checkUserName(userImportDTO, importUserMaps, memberIds);
            if (StringUtils.isBlank(userImportDTO.getErrorMsg())) {
                checkResult(userImportDTO, xpdRuleConfPO);
            }
            if (StringUtils.isBlank(userImportDTO.getErrorMsg()) && StringUtils.isBlank(userImportDTO.getReason())) {
                userImportDTO.setErrorMsg(i18nComponent.getI18nValue(ERR_REASON_EMPTY));
            }
            if (StringUtils.isBlank(userImportDTO.getErrorMsg())) {
                checkScore(userImportDTO, xpdRuleConfPO, dimResultMap);
            }
            if (StringUtils.isBlank(userImportDTO.getErrorMsg())) {
                checkMatch(userImportDTO, xpdRuleConfPO);
            }
            if (StringUtils.isNotEmpty(userImportDTO.getErrorMsg())) {
                // 数据异常，本条数据为异常数据不导入，跳出，执行下一条
                continue;
            }
            errorCount--;

            List<CaliUpdateUserResultReq> caliUpdateUserResultReqs = convertDmResult(userImportDTO.getDmResults(),xpdRuleConfPO.getResultType(), dimResultMap);

            if (!userIds.contains(userImportDTO.getUserId())){
                CaliUserImportDbDTO caliUserImportDbDTO = new CaliUserImportDbDTO();
                caliUserImportDbDTO.setUserId(userImportDTO.getUserId());
                caliUserImportDbDTO.setCaliUpdateUserResultReq(caliUpdateUserResultReqs);
                caliUserImportDbDTO.setImportType(1);
                caliUserImportDbDTO.setCaliId(caliId);
                caliUserImportDbDTO.setXpdId(xpdRuleConfPO.getXpdId());
                caliUserImportDbDTO.setResultType(xpdRuleConfPO.getResultType());
                caliUserImportDbDTO.setReason(userImportDTO.getReason());
                caliUserImportDbDTO.setSuggestion(userImportDTO.getRecommend());
                userResult.add(caliUserImportDbDTO);
            }
            userIds.add(userImportDTO.getUserId());
        }
        //6.3 保存数据
        log.debug("保存数据:{}", importDataList.size());
        caliMeetImportService.saveCaliUserImportData(orgId, userId, userResult);
        return CaliImportProcessedResult.builder() // NOSONAR
            .repeatCount(repeatCount)
//            .result(userResult)
            .totalData(importDataList)
            .failedData(StreamUtil.filterList(
                importDataList,
                b -> StringUtils.isNotBlank(b.getErrorMsg())))
            .successData(StreamUtil.filterList(
                importDataList,
                b -> StringUtils.isBlank(b.getErrorMsg())))
            .build();
    }

    private List<CaliUpdateUserResultReq> convertDmResult(Map<String, String> dmResults,int resultType, Map<String, CaliDmDTO> dimResultMap) {
        List<CaliUpdateUserResultReq> caliUpdateUserResultReqs = new ArrayList<>();
        for (Map.Entry<String, String> entry : dmResults.entrySet()) {
            CaliUpdateUserResultReq caliUpdateUserResultReq = new CaliUpdateUserResultReq();
            CaliDmDTO caliDmDTO = dimResultMap.get(entry.getKey());
            if (caliDmDTO != null) {
                caliUpdateUserResultReq.setSdDimId(caliDmDTO.getDmId());
                if (resultType == 0 && StringUtils.isNotBlank(entry.getValue())){
                    // 得分
                    caliUpdateUserResultReq.setScoreValue(new BigDecimal(entry.getValue()));
                    caliUpdateUserResultReqs.add(caliUpdateUserResultReq);
                }
                if (resultType == 1 && StringUtils.isNotBlank(entry.getValue())){
                    // 达标率
                    String qualifiedString = entry.getValue().replace("%","");
                    caliUpdateUserResultReq.setQualifiedPtg(new BigDecimal(qualifiedString));
                    caliUpdateUserResultReqs.add(caliUpdateUserResultReq);
                }
            }
        }
        return caliUpdateUserResultReqs;
    }

    private void checkMatch(CaliUserImportDTO userImportDTO,XpdRuleConfPO xpdRuleConfPO) {
        // 6.3 校验达标率
        if (xpdRuleConfPO.getResultType() == 1) {
            // 达标率
            for (Map.Entry<String, String> entry : userImportDTO.getDmResults().entrySet()) {
                if (StringUtils.isNotBlank(entry.getValue()) && isGreaterThan100(entry.getValue())){
                    userImportDTO.setErrorMsg(i18nComponent.getI18nValue(ERR_MATCH));
                    return;
                }
            }
        }
    }

    private void checkScore(CaliUserImportDTO userImportDTO, XpdRuleConfPO xpdRuleConfPO, Map<String, CaliDmDTO> dmDTOMap) {
        // 6.3 校验得分
        if (xpdRuleConfPO.getResultType() == 0) {
            // 分值
            for (Map.Entry<String, String> entry : userImportDTO.getDmResults().entrySet()) {
                BigDecimal totalScore = BigDecimal.ZERO;
                CaliDmDTO caliDmDTO = dmDTOMap.get(entry.getKey());
                if (caliDmDTO != null) {
                    totalScore = caliDmDTO.getTotalScore();
                }
                if (StringUtils.isNotBlank(entry.getValue()) && new BigDecimal(entry.getValue()).compareTo(totalScore) > 0){
                    userImportDTO.setErrorMsg(i18nComponent.getI18nValue(ERR_SCORE));
                    return;
                }
            }
        }
    }

    private void checkResult(CaliUserImportDTO userImportDTO, XpdRuleConfPO xpdRuleConfPO) {
        if (xpdRuleConfPO.getResultType() == 0) {
            // 分值
            for (Map.Entry<String, String> entry : userImportDTO.getDmResults().entrySet()) {
                if (StringUtils.isNotBlank(entry.getValue()) && !isValidFloatScore(entry.getValue())){
                    userImportDTO.setErrorMsg(i18nComponent.getI18nValue(ERR_RESULT));
                    return;
                }
            }
        }
        if (xpdRuleConfPO.getResultType() == 1) {
            // 达标率
            for (Map.Entry<String, String> entry : userImportDTO.getDmResults().entrySet()) {
                if (StringUtils.isNotBlank(entry.getValue()) && !isValidPercentageScore(entry.getValue())){
                    userImportDTO.setErrorMsg(i18nComponent.getI18nValue(ERR_RESULT));
                    return;
                }
            }
        }

    }

    public boolean isValidFloatScore(String score) {
        return score.matches("\\d+(\\.\\d+)?"); // 整数或小数
    }

    public boolean isValidPercentageScore(String score) {
        // 正则表达式匹配带 % 的浮点数
        return score.matches("^(\\d+(\\.\\d+)?)(%)$");
    }

    public boolean isGreaterThan100(String percentageStr) {
        // 检查字符串是否以 '%' 结尾
        if (percentageStr == null || !percentageStr.endsWith("%")) {
            return false;
        }

        // 去掉 '%' 并将字符串转换为 BigDecimal
        String numberStr = percentageStr.substring(0, percentageStr.length() - 1);
        try {
            BigDecimal percentageValue = new BigDecimal(numberStr);
            return percentageValue.compareTo(new BigDecimal("100")) > 0; // 判断是否大于 100
        } catch (NumberFormatException e) {
            System.out.println("Invalid number format: " + numberStr);
            return false; // 或者抛出异常
        }
    }

    private Map<String, CaliDmDTO> getDimResult(String orgId, String xpdId) {
        List<CaliDmDTO> dms = caliMeetImportService.getDms(orgId, xpdId, true);
        //6.3 获取盘点项目维度
        if (CollectionUtils.isEmpty(dms)){
            throw new ApiException(ExceptionKeys.CALI_USER_IMPORT_ERROR_NO_DIMS);
        }
        return StreamUtil.list2map(dms, CaliDmDTO::getDmName);
    }

    private List<String> getCaliUsers(String orgId, String caliId) {
        //6.3 获取校准会成员
        return calimeetUserMapper.getCaliMeetUserIds(orgId, caliId);
    }

    private String dealSpecialSymbol(String param) {
        if (StringUtils.isBlank(param)) {
            return param;
        }
        // 保留换行符，不做替换处理
        return param;
    }

    /**
     * 用户账户check
     */
    private void checkUserName(
        CaliUserImportDTO userImportDTO, Map<String, UdpLiteUserPO> importUserMaps, List<String> memberIds) {
        // 不填
        if (StringUtils.isBlank(userImportDTO.getUserName())) {
            userImportDTO.setErrorMsg(i18nComponent.getI18nValue(ERR_USERNAME_NOT_EXISTED));
            return;
        }
        // 用户信息
        UdpLiteUserPO user = importUserMaps.get(userImportDTO.getUserName());
        // 不存在
        if (user == null || StringUtils.isBlank(user.getId())) {
            userImportDTO.setErrorMsg(i18nComponent.getI18nValue(ERR_USERNAME_NOT_EXISTED));
            return;
        }
        // 删除
        if (user.getDeleted().intValue() == DeleteEnum.DELETED.getCode()) {
            userImportDTO.setErrorMsg(i18nComponent.getI18nValue(ERR_USERNAME_NOT_EXISTED));
            return;
        }
        // 禁用
        if (user.getStatus().intValue() == EnableEnum.DISABLED.getCode()) {
            userImportDTO.setErrorMsg(i18nComponent.getI18nValue(ERR_USERNAME_DISABLED));
        }
        // 不存在于当前校准会中
        if (!memberIds.contains(user.getId())) {
            userImportDTO.setErrorMsg(i18nComponent.getI18nValue(ERR_USERNAME_NOT_IN_CALIS));
        }
        userImportDTO.setUserId(user.getId());
    }

    private List<UdpLiteUserPO> getImportUsers(
        List<CaliUserImportDTO> perfImportDtos, String orgId) {
        Set<String> importUsernames = new HashSet<>(perfImportDtos.size());
        perfImportDtos.forEach(
            performance4Import -> importUsernames.add(performance4Import.getUserName()));
        return udpLiteUserMapper.selectActiveUsersByUserNames(orgId, importUsernames);
    }

    @Setter
    @Getter
    @ToString
    @SuperBuilder
    static class CaliImportProcessedResult extends FileProcessedResult<CaliUserImportDTO> {
        public final int repeatCount;
        public final Map<String, List<CaliUserImportDTO>> result;
    }

    @RequiredArgsConstructor
    private class CaliDimLevelFileReader extends FileReader<CaliUserImportDTO> {

        private final MultipartFile file;

        private final String fileId;

        @Override
        @jakarta.annotation.Nonnull
        public List<CaliUserImportDTO> read() {
            return doReadExcel();
        }

        /**
         * 由于导入文件中有部分表头是动态生成的, 所以这里没办法使用静态导入的方式, 而是采用动态解析的方式来处理导入的数据
         */
        @jakarta.annotation.Nonnull
        private List<CaliUserImportDTO> doReadExcel() {
            ExcelReader excelReader = null;
            List<CaliUserImportDTO> importDataList;
            try (
                InputStream inputStream = this.getInputStream(file, fileId)
            ) {
                excelReader = EasyExcelFactory.read(inputStream).build();
                ReadSheet sheet = excelReader.excelExecutor().sheetList().get(0);

                // 读取sheet
                EasyExcelListener listener = new EasyExcelListener();
                sheet.setCustomReadListenerList(Collections.singletonList(listener));
                sheet.setHeadRowNumber(1);
                excelReader.read(sheet);

                // 提取Excel数据
                importDataList = generateUser4ExcelImportList(listener);
            } catch (IOException e) {
                throw new ApiException(e.getMessage());
            } finally {
                if (excelReader != null) {
                    excelReader.finish();
                }
            }
            return importDataList;
        }


        @jakarta.annotation.Nonnull
        private List<CaliUserImportDTO> generateUser4ExcelImportList(
            EasyExcelListener listener) {

            List<Map<Integer, String>> list = listener.getData();
            List<CaliUserImportDTO> muList = new ArrayList<>(list.size());

            // 表头
            Map<Integer, String> headMap = list.get(0);
            int allcellSize = headMap.size();
            if (headMap.isEmpty() || headMap.size() < 4) {
                throw new ApiException(ExceptionKeys.APIS_CALI_IMPORT_ERROR);
            }

            try {
                // 内容
                for (int j = 1; j < list.size(); j++) {
                    Map<Integer, String> map = list.get(j);
                    int i = 0;
                    CaliUserImportDTO temp = new CaliUserImportDTO();
                    temp.setFullName(map.get(i++));
                    temp.setUserName(map.get(i++));
                    for (; i < allcellSize; ) {
                        if (i == allcellSize - 2){
                            temp.setReason(map.get(i++));
                        }else if (i == allcellSize - 1){
                            temp.setRecommend(map.get(i++));
                        }else {
                            String headName = headMap.get(i);
                            if (headName.contains("校准后")){
                                String dmName = headName.replace("校准后","");
                                temp.getDmResults().put(dmName, map.get(i));
                            }
                            temp.getOriginDmResults().put(headName, map.get(i));
                            i++;
                        }
                    }
                    muList.add(temp);
                }
            } catch (Exception e) {
                throw new ApiException(ExceptionKeys.APIS_CALI_IMPORT_ERROR);
            }
            return muList;
        }
    }
}
