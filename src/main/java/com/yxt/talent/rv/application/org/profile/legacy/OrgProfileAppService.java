package com.yxt.talent.rv.application.org.profile.legacy;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.yxt.common.exception.ApiException;
import com.yxt.common.pojo.IdName;
import com.yxt.common.service.AuthService;
import com.yxt.common.service.ILock;
import com.yxt.common.service.MessageSourceService;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.StreamUtil;
import com.yxt.common.util.Validate;
import com.yxt.export.DlcComponent;
import com.yxt.export.I18nComponent;
import com.yxt.spmodel.facade.bean.sql.SqlParam;
import com.yxt.talent.rv.application.dmp.user.expt.TeamDmpUserDimResultExportStrategy;
import com.yxt.talent.rv.application.org.profile.SpmodelSqlBuilder;
import com.yxt.talent.rv.application.org.profile.dto.*;
import com.yxt.talent.rv.application.org.profile.model.*;
import com.yxt.talent.rv.application.xpd.result.dto.UserDimensionDataDTO;
import com.yxt.talent.rv.controller.client.bizmgr.dept.viewobj.OrgProfileBasicClientVO;
import com.yxt.talent.rv.controller.client.bizmgr.team.viewobj.OrgProfileSexInfoDTO;
import com.yxt.talent.rv.controller.client.bizmgr.team.viewobj.TeamOrgProfileBasicClientVO;
import com.yxt.talent.rv.controller.manage.org.query.OrgProfileUserDimGridQuery;
import com.yxt.talent.rv.controller.manage.org.viewobj.*;
import com.yxt.talent.rv.controller.manage.xpd.grid.viewobj.XpdGridLevelVO;
import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdResultUserDimDetailVO;
import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdResultUserDimLevelVO;
import com.yxt.talent.rv.domain.dept.Dept;
import com.yxt.talent.rv.domain.dept.DeptDomainRepo;
import com.yxt.talent.rv.infrastructure.common.constant.ExceptionKeys;
import com.yxt.talent.rv.infrastructure.common.constant.RedisKeys;
import com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil;
import com.yxt.talent.rv.infrastructure.common.utilities.util.DateTimeUtil;
import com.yxt.talent.rv.infrastructure.config.AppProperties;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpDeptMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdGridLevelMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdGridMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridLevelPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridPO;
import com.yxt.talent.rv.infrastructure.service.file.FileConstants;
import com.yxt.talent.rv.infrastructure.service.file.dto.DynamicExcelExportContent;
import com.yxt.talent.rv.infrastructure.service.file.viewobj.GenericFileExportVO;
import com.yxt.talent.rv.infrastructure.service.remote.SpmodelAclService;
import com.yxt.talentrvfacade.bean.UserDimGrid4Info;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.fasterxml.jackson.annotation.JsonInclude.Include.ALWAYS;
import static com.yxt.common.util.BeanHelper.bean2Json;
import static com.yxt.common.util.StreamUtil.list2map;
import static com.yxt.talent.rv.application.org.profile.SpmodelSqlBuilder.buildDeptRvDetailQuerySql;
import static com.yxt.talent.rv.application.org.profile.SpmodelSqlBuilder.buildEvalSkillQuery4Matrix;
import static com.yxt.talent.rv.application.org.profile.SpmodelSqlBuilder.buildFirstDeptIdQuerySql;
import static com.yxt.talent.rv.application.org.profile.SpmodelSqlBuilder.buildPrjDimGridQuerySql;
import static com.yxt.talent.rv.application.org.profile.SpmodelSqlBuilder.buildTeamAvgAgeAndServiceYearsQuerySql;
import static com.yxt.talent.rv.application.org.profile.SpmodelSqlBuilder.buildTeamProfileSexRatioQuerySql;
import static com.yxt.talent.rv.application.org.profile.SpmodelSqlBuilder.buildUserRvDetailQuerySql;
import static com.yxt.talent.rv.application.org.profile.SpmodelTableConstants.DWM_DEPT;
import static com.yxt.talent.rv.application.org.profile.SpmodelTableConstants.DWM_DEPT_AGE;
import static com.yxt.talent.rv.application.org.profile.SpmodelTableConstants.DWM_DEPT_EDUCATION;
import static com.yxt.talent.rv.application.org.profile.SpmodelTableConstants.DWM_DEPT_EMPDUR;
import static com.yxt.talent.rv.application.org.profile.SpmodelTableConstants.DWM_DEPT_JOBGRADE;
import static com.yxt.talent.rv.application.org.profile.SpmodelTableConstants.DWM_DEPT_POSITION;
import static com.yxt.talent.rv.application.org.profile.SpmodelTableConstants.DWM_DEPT_POSITION_JOBGRADE;
import static com.yxt.talent.rv.application.org.profile.SpmodelTableConstants.DWM_DEPT_RV_DETAIL;
import static com.yxt.talent.rv.application.org.profile.SpmodelTableConstants.DWM_DEPT_SERIES_RV;
import static com.yxt.talent.rv.application.org.profile.SpmodelTableConstants.DWS_DEPT;
import static com.yxt.talent.rv.infrastructure.common.transfer.TransferSupport.MAX_LEASE_TIME;
import static com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil.distinct;
import static com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil.filterNullAndBlank;
import static com.yxt.talent.rv.infrastructure.common.utilities.util.MathUtil.divide;
import static com.yxt.talent.rv.infrastructure.common.utilities.util.MathUtil.dividePer;
import static com.yxt.talent.rv.infrastructure.common.utilities.util.MathUtil.null2zero;
import static com.yxt.talent.rv.infrastructure.common.utilities.util.SqlUtil.buildSqlQuery;
import static com.yxt.talent.rv.infrastructure.common.utilities.util.SqlUtil.jsonObject2Bean;
import static com.yxt.talent.rv.infrastructure.common.utilities.util.SqlUtil.jsonObject2BeanList;
import static com.yxt.talent.rv.infrastructure.common.utilities.util.SqlUtil.queryListFromModel;
import static java.math.RoundingMode.HALF_UP;
import static java.util.Arrays.asList;
import static java.util.stream.Collectors.groupingBy;
import static org.apache.commons.lang3.StringUtils.EMPTY;
import static org.apache.commons.lang3.StringUtils.isBlank;
import static org.apache.commons.lang3.StringUtils.isNotBlank;

/**
 * 组织画像
 *
 * @deprecated 由于组织画像已划定到Org领域，所有请求应使用OrgCmdAppService和OrgQryAppService应用服务类
 */
@Slf4j
@Component
@Deprecated(since = "5.2")
@RequiredArgsConstructor
public class OrgProfileAppService {

    public static final String PARENT_ID = "parent_id";
    public static final String ORG_ID = "org_id";
    public static final String SERIES_ROOT_ID = "series_root_id";
    public static final String DEPT_ID = "dept_id";
    public static final String YEARLY = "yearly";
    public static final String DELETED = "deleted";
    public static final String SERIES_ORDER = "series_order";

    // @format:off
    private static final String[] SHEET_1_HEADER_KEYS =
        {"cataName", "dimName", "achievedUserCount", "unAchievedUserCount", "achievedRate"};
    private static final String SHEET_1_HEADER_PREFIX = "apis.sptalentrv.team.rv.detail.export.sheet1.header.";
    private static final String[] SHEET_2_HEADER_KEYS = {"cataName", "dimName", "fullname", "username", "achieved"};
    private static final String SHEET_2_HEADER_PREFIX = "apis.sptalentrv.team.rv.detail.export.sheet2.header.";
    private static final String SHEET_1_NAME = "apis.sptalentrv.team.rv.detail.export.sheet1.name";
    private static final String SHEET_2_NAME = "apis.sptalentrv.team.rv.detail.export.sheet2.name";
    // @format:on
    private final SpmodelAclService spmodelAclService;
    private final DeptDomainRepo deptDomainRepo;
    private final UdpLiteUserMapper udpLiteUserMapper;
    private final AuthService authService;
    private final MessageSourceService messageSourceService;
    private final I18nComponent i18nComponent;
    private final DlcComponent dlcComponent;
    private final TeamDmpUserDimResultExportStrategy teamDmpUserDimResultExportStrategy;
    private final ILock lockService;
    private final UdpDeptMapper udpDeptMapper;
    private final XpdGridMapper xpdGridMapper;
    private final XpdGridLevelMapper xpdGridLevelMapper;
    private final AppProperties appProperties;


    public OrgProfileManageSeriesRvVO getManageSeriesRv(String orgId, String yearly, String deptId) {
        OrgProfileManageSeriesRvVO result = new OrgProfileManageSeriesRvVO();
        result.setYearly(Integer.valueOf(yearly));

        Map<String, Object> queryParams = new HashMap<>(8);
        queryParams.put(YEARLY, yearly);
        queryParams.put(ORG_ID, orgId);
        queryParams.put(DEPT_ID, isNotBlank(deptId) ? deptId : getRootDeptId(orgId));
        queryParams.put(DELETED, 0);
        SqlParam sqlParam = new SqlParam();
        sqlParam.setOrgId(orgId);
        sqlParam.setSql(buildSqlQuery(DwsDept.class, queryParams));

        List<DwsDept> deptRvs = jsonObject2BeanList(DwsDept.class, spmodelAclService.sql(sqlParam));
        if (CollectionUtils.isNotEmpty(deptRvs)) {
            DwsDept dwsDept = deptRvs.get(0);
            result.setManageCompCount(dwsDept.getManageCompCount());
            result.setManageUnCompCount(dwsDept.getManageUncompCount());
        }

        LinkedHashMap<String, Boolean> orderByFields = new LinkedHashMap<>(8);
        orderByFields.put(SERIES_ORDER, true);
        sqlParam.setSql(buildSqlQuery(DwmDeptSeriesRv.class, queryParams, orderByFields));
        List<DwmDeptSeriesRv> deptSeriesRvs =
            jsonObject2BeanList(DwmDeptSeriesRv.class, spmodelAclService.sql(sqlParam));
        if (CollectionUtils.isNotEmpty(deptSeriesRvs)) {
            List<OrgProfileManageSeriesRvDTO> seriesRvs = new ArrayList<>();
            Map<String, List<DwmDeptSeriesRv>> deptSeriesRvsMap =
                deptSeriesRvs.stream().collect(Collectors.groupingBy(DwmDeptSeriesRv::getSeriesRootId));
            Map<String, DwmDeptSeriesRv> deptSeriesRvMap =
                StreamUtil.list2map(deptSeriesRvs, DwmDeptSeriesRv::getSeriesRootId);
            for (Map.Entry<String, List<DwmDeptSeriesRv>> entry : deptSeriesRvsMap.entrySet()) {
                OrgProfileManageSeriesRvDTO seriesRv = new OrgProfileManageSeriesRvDTO();
                DwmDeptSeriesRv dwmDeptSeriesRv = deptSeriesRvMap.get(entry.getKey());
                seriesRv.setSeriesName(dwmDeptSeriesRv.getSeriesRootName());
                seriesRv.setOrderIndex(dwmDeptSeriesRv.getSeriesOrder());

                List<OrgProfileBaseInfoDTO> orgProfileBaseInfoDtos = new ArrayList<>();
                Map<Integer, List<DwmDeptSeriesRv>> competentMaps =
                    entry.getValue().stream().collect(Collectors.groupingBy(DwmDeptSeriesRv::getCompetent));

                Map<Integer, Integer> manageUserCount =
                    getSeriesManageRvUserCount(orgId, deptId, dwmDeptSeriesRv.getSeriesRootId());
                for (Map.Entry<Integer, List<DwmDeptSeriesRv>> competentMap : competentMaps.entrySet()) {
                    OrgProfileBaseInfoDTO orgProfileBaseInfoDTO = new OrgProfileBaseInfoDTO();
                    orgProfileBaseInfoDTO.setOrderIndex(competentMap.getKey() * -1);
                    orgProfileBaseInfoDTO.setName(competentMap.getKey() == 0 ? "未胜任" : "胜任");
                    orgProfileBaseInfoDTO.setCount(null == manageUserCount.get(competentMap.getKey()) ? 0 :
                        manageUserCount.get(competentMap.getKey()));
                    orgProfileBaseInfoDTO.setCompetent(competentMap.getKey());
                    orgProfileBaseInfoDtos.add(orgProfileBaseInfoDTO);
                }

                orgProfileBaseInfoDtos = orgProfileBaseInfoDtos.stream()
                    .sorted(Comparator.comparing(OrgProfileBaseInfoDTO::getOrderIndex))
                    .collect(Collectors.toList());
                seriesRv.setSeriesrvs(orgProfileBaseInfoDtos);
                seriesRvs.add(seriesRv);
            }
            seriesRvs = seriesRvs.stream()
                .sorted(Comparator.comparing(OrgProfileManageSeriesRvDTO::getOrderIndex))
                .collect(Collectors.toList());
            result.setSeriesrvs(seriesRvs);
        }

        return result;
    }

    private Map<Integer, Integer> getSeriesManageRvUserCount(String orgId, String deptId, String seriesRootId) {
        Map<Integer, Integer> userCountMap = new HashMap<>(8);
        Map<String, Object> queryParams = new HashMap<>(8);
        queryParams.put("manage", 1);
        queryParams.put(SERIES_ROOT_ID, seriesRootId);
        if (isNotBlank(deptId)) {
            Map<String, Object> deptParams = new HashMap<>(8);
            deptParams.put(ORG_ID, orgId);
            deptParams.put(PARENT_ID, deptId);
            SqlParam sqlParam = new SqlParam();
            sqlParam.setOrgId(orgId);
            sqlParam.setSql(buildSqlQuery(DimDeptClosure.class, deptParams));
            List<DimDeptClosure> depts = jsonObject2BeanList(DimDeptClosure.class, spmodelAclService.sql(sqlParam));
            List<String> deptIds = StreamUtil.mapList(depts, DimDeptClosure::getDeptId);
            deptIds.add(deptId);
            Object[] arrayValue = deptIds.toArray();
            queryParams.put(DEPT_ID, arrayValue);
        }
        queryParams.put(DELETED, 0);
        SqlParam sqlParam = new SqlParam();
        sqlParam.setOrgId(orgId);
        sqlParam.setSql(buildSqlQuery(DwdUserRv.class, queryParams));

        List<DwdUserRv> deptRvs = jsonObject2BeanList(DwdUserRv.class, spmodelAclService.sql(sqlParam));
        if (CollectionUtils.isNotEmpty(deptRvs)) {
            Map<Integer, List<DwdUserRv>> competentMaps =
                deptRvs.stream().collect(Collectors.groupingBy(DwdUserRv::getCompetent));
            for (Map.Entry<Integer, List<DwdUserRv>> competentMap : competentMaps.entrySet()) {
                userCountMap.put(competentMap.getKey(), competentMap.getValue().size());
            }
        }
        return userCountMap;
    }

    public List<OrgProfileDeptRvVO> getDeptRv(String orgId, String yearly, String deptId) {
        List<OrgProfileDeptRvVO> result = new ArrayList<>();
        String rootDeptId = getRootDeptId(orgId);
        if (isBlank(rootDeptId)) {
            log.warn("LOG61100:root dept id is blank, orgId={}", orgId);
            return Collections.emptyList();
        }
        Map<String, Object> queryParams = new HashMap<>(8);
        queryParams.put(ORG_ID, orgId);
        queryParams.put(DEPT_ID, isNotBlank(deptId) ? deptId : getRootDeptId(orgId));
        queryParams.put(YEARLY, new String[]{yearly, Integer.parseInt(yearly) - 1 + ""});
        queryParams.put(DELETED, 0);
        SqlParam sqlParam = new SqlParam();
        sqlParam.setOrgId(orgId);
        sqlParam.setSql(buildSqlQuery(DwsDept.class, queryParams));

        List<DwsDept> deptRvs = jsonObject2BeanList(DwsDept.class, spmodelAclService.sql(sqlParam));
        if (CollectionUtils.isNotEmpty(deptRvs)) {
            deptRvs = deptRvs.stream()
                .sorted(Comparator.comparing(DwsDept::getYearly).reversed())
                .collect(Collectors.toList());
            deptRvs.forEach(e -> {
                OrgProfileDeptRvVO orgProfileDeptRvVO = new OrgProfileDeptRvVO();
                int compCount = null2zero(e.getCompCount());
                int uncompCount = null2zero(e.getUncompCount());
                int totalUserCount = compCount + uncompCount; // Calculate total count once
                orgProfileDeptRvVO.setCompetentRate(divide(compCount, totalUserCount, 4, HALF_UP).doubleValue());
                orgProfileDeptRvVO.setYearly(e.getYearly());
                orgProfileDeptRvVO.setCompCount(compCount);
                orgProfileDeptRvVO.setUserCount(totalUserCount); // Reuse total count and cast to int
                orgProfileDeptRvVO.setRvCount(null2zero(e.getRvCount()));
                orgProfileDeptRvVO.setUncompCount(null2zero(e.getUncompCount()));
                result.add(orgProfileDeptRvVO);
            });
        }
        return result;
    }

    /**
     * 获取机构根部门id
     *
     * @param orgId
     * @return
     */
    private String getRootDeptId(String orgId) {
        return udpDeptMapper.selectRootDeptId(orgId);
    }

    public OrgProfileBaseInfoVO getDeptBaseInfo(String orgId, String yearly, String deptId) {
        OrgProfileBaseInfoVO info4Get = new OrgProfileBaseInfoVO();
        Map<String, Object> queryParam = new HashMap<>(8);
        queryParam.put(ORG_ID, orgId);
        queryParam.put(YEARLY, yearly);
        queryParam.put(DEPT_ID, isNotBlank(deptId) ? deptId : getRootDeptId(orgId));
        queryParam.put(DELETED, 0);
        SqlParam sqlParam = new SqlParam();
        sqlParam.setOrgId(orgId);
        try {
            info4Get.setJobGrades(getBasicInfo(DwmDeptJobgrade.class, DWM_DEPT_JOBGRADE, "thirdJobgradeName", queryParam, sqlParam, false));
            info4Get.setEmpdurs(getBasicInfo(DwmDeptEmpdur.class, DWM_DEPT_EMPDUR, "empdurName", queryParam, sqlParam, false));
            List<OrgProfileBaseInfoDTO> thirdPositionName = getBasicInfo(DwmDeptPosition.class, DWM_DEPT_POSITION, "thirdPositionName", queryParam, sqlParam, false);
            info4Get.setPositions(thirdPositionName.stream()
                .sorted(Comparator.comparing(OrgProfileBaseInfoDTO::getCount).reversed())
                .limit(10)
                .collect(Collectors.toList()));

            Map<String, Object> deptQueryParam = new HashMap<>(queryParam);
            String[] firstDeptIds = getFirstDeptId(String.valueOf(queryParam.get(ORG_ID)), String.valueOf(queryParam.get(DEPT_ID)));
            if (firstDeptIds.length > 0) {
                deptQueryParam.put(DEPT_ID, firstDeptIds);
                log.debug("LOG66160:{}", bean2Json(deptQueryParam, ALWAYS));
                info4Get.setDepts(getBasicInfo(DwmDept.class, DWM_DEPT, "thirdDeptName", deptQueryParam, sqlParam, false));
            } else {
                info4Get.setDepts(new ArrayList<>());
            }
        } catch (Exception e) {
            log.error("LOG61900:getDeptBaseInfo error", e);
        }
        return info4Get;
    }

    public OrgProfileBasicClientVO getManageDeptBaseInfo(String orgId, String yearly, String deptId, boolean ifManage) {
        OrgProfileBasicClientVO info4Get = new OrgProfileBasicClientVO();
        Map<String, Object> queryParam = new HashMap<>(8);
        queryParam.put(YEARLY, yearly);
        queryParam.put(ORG_ID, orgId);
        queryParam.put(DEPT_ID, isNotBlank(deptId) ? deptId : getRootDeptId(orgId));
        queryParam.put(DELETED, 0);
        SqlParam sqlParam = new SqlParam();
        sqlParam.setOrgId(orgId);
        try {
            List<OrgProfileBaseInfoDTO> educations = getBasicInfo(DwmDeptEducation.class, DWM_DEPT_EDUCATION, "educationName", queryParam, sqlParam, ifManage);
            info4Get.setEducations(educations.stream().filter(e -> e.getCount() != null && e.getCount() > 0).collect(Collectors.toList()));
            info4Get.setEmpdurs(getBasicInfo(DwmDeptEmpdur.class, DWM_DEPT_EMPDUR, "empdurName", queryParam, sqlParam, ifManage));
            info4Get.setAges(getBasicInfo(DwmDeptAge.class, DWM_DEPT_AGE, "ageName", queryParam, sqlParam, ifManage));
            // 序列人数分布
            info4Get.setSeriesList(getSeriesBasicInfo(queryParam, sqlParam));

        } catch (Exception e) {
            log.error("LOG61910:getDeptBaseInfo error", e);
        }
        return info4Get;
    }

    private List<OrgProfileBaseInfoDTO> getSeriesBasicInfo(Map<String, Object> queryParam, SqlParam sqlParam) {
        List<OrgProfileBaseInfoDTO> baseInfoBeans = new ArrayList<>();
        LinkedHashMap<String, Boolean> orderByFields = new LinkedHashMap<>(8);
        orderByFields.put(SERIES_ORDER, true);
        sqlParam.setSql(buildSqlQuery(DwmDeptSeriesPosition.class, queryParam, orderByFields));
        List<DwmDeptSeriesPosition> seriesPositionList =
            jsonObject2BeanList(DwmDeptSeriesPosition.class, spmodelAclService.sql(sqlParam));
        if (CollectionUtils.isNotEmpty(seriesPositionList)) {
            Map<String, List<DwmDeptSeriesPosition>> seriesMap =
                seriesPositionList.stream().collect(Collectors.groupingBy(DwmDeptSeriesPosition::getSeriesRootId));
            for (Map.Entry<String, List<DwmDeptSeriesPosition>> entry : seriesMap.entrySet()) {
                List<DwmDeptSeriesPosition> dwmDeptSeriesPositions = entry.getValue();
                if (CollectionUtils.isNotEmpty(dwmDeptSeriesPositions)) {
                    OrgProfileBaseInfoDTO baseInfoBean = new OrgProfileBaseInfoDTO();
                    baseInfoBean.setOrderIndex(dwmDeptSeriesPositions.get(0).getSeriesOrder());
                    baseInfoBean.setName(dwmDeptSeriesPositions.get(0).getSeriesRootName());
                    baseInfoBean.setCount(dwmDeptSeriesPositions.stream().mapToInt(DwmDeptSeriesPosition::getManageCount).sum());
                    baseInfoBeans.add(baseInfoBean);
                }
            }
        }
        return baseInfoBeans;
    }

    private List<OrgProfileBaseInfoDTO> getBasicInfo(
            Class<?> clazz, String tableName, String name, Map<String, Object> queryParam, SqlParam sqlParam, boolean ifManage) {
        String countName = ifManage ? "manageCount" : "userCount";
        List<OrgProfileBaseInfoDTO> baseInfoBeans = new ArrayList<>();
        LinkedHashMap<String, Boolean> orderByFields = new LinkedHashMap<>(8);
        orderByFields.put("order_index", true);
        sqlParam.setSql(buildSqlQuery(clazz, tableName, queryParam, orderByFields));
        List<JSONObject> jsonObjects = spmodelAclService.sql(sqlParam);
        jsonObjects.forEach(jsonObject -> {
            OrgProfileBaseInfoDTO baseInfoBean = new OrgProfileBaseInfoDTO();
            baseInfoBean.setCount(jsonObject.getInteger(countName));
            baseInfoBean.setName(jsonObject.getString(name));
            baseInfoBean.setOrderIndex(jsonObject.getInteger("orderIndex"));
            if (isNotBlank(baseInfoBean.getName())) {
                baseInfoBeans.add(baseInfoBean);
            }
        });
        return baseInfoBeans;
    }

    public List<OrgProfileRvSeriesVO> getRvSeries(String orgId, String yearly, String deptId) {
        List<OrgProfileRvSeriesVO> portraitRvSeries4Gets = new ArrayList<>();
        Map<String, Object> queryParams = new HashMap<>(8);
        queryParams.put(ORG_ID, orgId);
        queryParams.put(DEPT_ID, isNotBlank(deptId) ? deptId : getRootDeptId(orgId));
        queryParams.put(DELETED, 0);
        queryParams.put(YEARLY, yearly);
        LinkedHashMap<String, Boolean> orderByFields = new LinkedHashMap<>(8);
        orderByFields.put(SERIES_ORDER, true);

        SqlParam sqlParam = new SqlParam();
        sqlParam.setOrgId(orgId);
        sqlParam.setSql(buildSqlQuery(DwmDeptSeriesRv.class, DWM_DEPT_SERIES_RV, queryParams, orderByFields));

        List<DwmDeptSeriesRv> deptSeriesRvs =
            jsonObject2BeanList(DwmDeptSeriesRv.class, spmodelAclService.sql(sqlParam));
        if (CollectionUtils.isNotEmpty(deptSeriesRvs)) {
            Map<String, List<DwmDeptSeriesRv>> deptSeriesRvsMap =
                deptSeriesRvs.stream().collect(Collectors.groupingBy(DwmDeptSeriesRv::getSeriesRootId));
            Map<String, DwmDeptSeriesRv> deptSeriesRvMap =
                StreamUtil.list2map(deptSeriesRvs, DwmDeptSeriesRv::getSeriesRootId);
            for (Map.Entry<String, List<DwmDeptSeriesRv>> entry : deptSeriesRvsMap.entrySet()) {
                OrgProfileRvSeriesVO seriesRv = new OrgProfileRvSeriesVO();
                DwmDeptSeriesRv dwmDeptSeriesRv = deptSeriesRvMap.get(entry.getKey());
                seriesRv.setSeriesId(dwmDeptSeriesRv.getSeriesRootId());
                seriesRv.setSeriesName(dwmDeptSeriesRv.getSeriesRootName());
                seriesRv.setPositionCount(dwmDeptSeriesRv.getPositionCount());
                seriesRv.setOrderIndex(dwmDeptSeriesRv.getSeriesOrder());

                int userSumCount = 0;
                int competentSumCount = 0;
                for (DwmDeptSeriesRv e : entry.getValue()) {
                    int userCount = null2zero(e.getUserCount());
                    userSumCount += userCount;
                    if (1 == e.getCompetent()) {
                        competentSumCount += userCount;
                    }
                }

                seriesRv.setUserCount(userSumCount);
                seriesRv.setRvRate(divide(competentSumCount, userSumCount, 4, HALF_UP).doubleValue());
                seriesRv.setCompCount(competentSumCount);
                seriesRv.setUnCompCount(Math.max(userSumCount - competentSumCount, 0));
                portraitRvSeries4Gets.add(seriesRv);
            }
        }
        portraitRvSeries4Gets = portraitRvSeries4Gets.stream()
            .sorted(Comparator.comparing(OrgProfileRvSeriesVO::getOrderIndex))
            .collect(Collectors.toList());

        return portraitRvSeries4Gets;
    }

    public OrgProfileRvSeriesDmpVO getRvSeriesDetail(String orgId, String seriesId, String deptId, String yearly) {
        OrgProfileRvSeriesDmpVO result = new OrgProfileRvSeriesDmpVO();
        Map<String, Object> queryParams = new HashMap<>(8);
        if (isNotBlank(seriesId)) {
            queryParams.put(SERIES_ROOT_ID, seriesId);
        }
        queryParams.put(YEARLY, yearly);
        queryParams.put(ORG_ID, orgId);
        queryParams.put(DEPT_ID, isNotBlank(deptId) ? deptId : getRootDeptId(orgId));
        queryParams.put(DELETED, 0);

        LinkedHashMap<String, Boolean> orderByFields = new LinkedHashMap<>(8);
        orderByFields.put("position_order", true);
        SqlParam sqlParam1 = new SqlParam();
        sqlParam1.setOrgId(orgId);
        sqlParam1.setSql(buildSqlQuery(DwmDeptSeriesPositionRv.class, queryParams, orderByFields));
        List<DwmDeptSeriesPositionRv> deptSeriesPositionRvs =
            jsonObject2BeanList(DwmDeptSeriesPositionRv.class, spmodelAclService.sql(sqlParam1));
        if (CollectionUtils.isNotEmpty(deptSeriesPositionRvs)) {
            List<OrgProfileRvSeriesDmpCompetentDTO> positionRv = new ArrayList<>();
            Map<String, List<DwmDeptSeriesPositionRv>> deptSeriesPositionRvMap = deptSeriesPositionRvs.stream()
                .collect(Collectors.groupingBy(DwmDeptSeriesPositionRv::getPositionId));
            Map<String, DwmDeptSeriesPositionRv> deptSeriesRvMap =
                StreamUtil.list2map(deptSeriesPositionRvs, DwmDeptSeriesPositionRv::getPositionId);
            for (Map.Entry<String, List<DwmDeptSeriesPositionRv>> e : deptSeriesPositionRvMap.entrySet()) {
                OrgProfileRvSeriesDmpCompetentDTO dmpCompetentDetailDTO = new OrgProfileRvSeriesDmpCompetentDTO();
                DwmDeptSeriesPositionRv dwmDeptSeriesPositionRv = deptSeriesRvMap.get(e.getKey());
                dmpCompetentDetailDTO.setName(dwmDeptSeriesPositionRv.getThirdPositionName());
                dmpCompetentDetailDTO.setOrderIndex(dwmDeptSeriesPositionRv.getPositionOrder());
                int userSumCount = 0;
                int competentSumCount = 0;
                for (DwmDeptSeriesPositionRv deptSeriesPositionRv : e.getValue()) {
                    int userCount = null2zero(deptSeriesPositionRv.getUserCount());
                    userSumCount += userCount;
                    if (1 == deptSeriesPositionRv.getCompetent()) {
                        competentSumCount += userCount;
                    }
                }
                dmpCompetentDetailDTO.setUserCount(userSumCount);
                dmpCompetentDetailDTO.setCompetentUserCount(competentSumCount);
                dmpCompetentDetailDTO.setRvRate(divide(competentSumCount, userSumCount, 4, HALF_UP).doubleValue());
                positionRv.add(dmpCompetentDetailDTO);
            }
            positionRv = positionRv.stream()
                .sorted(Comparator.comparing(OrgProfileRvSeriesDmpCompetentDTO::getOrderIndex))
                .collect(Collectors.toList());
            result.setPositionRv(positionRv);
        }

        SqlParam sqlParam2 = new SqlParam();
        sqlParam2.setOrgId(orgId);
        sqlParam2.setSql(buildSqlQuery(DwmDeptRvDetail.class, DWM_DEPT_RV_DETAIL, queryParams, null));
        List<DwmDeptRvDetail> dwmDeptRvDetails = jsonObject2BeanList(DwmDeptRvDetail.class, spmodelAclService.sql(sqlParam2));
        if (CollectionUtils.isNotEmpty(dwmDeptRvDetails)) {
            List<OrgProfileRvSeriesDetailRateDTO> rvs = new ArrayList<>();
            List<OrgProfileRvSeriesDetaiCataDTO> catas = new ArrayList<>();
            List<String> cataIds = new ArrayList<>();
            dwmDeptRvDetails.forEach(e -> {
                OrgProfileRvSeriesDetailRateDTO rvSeriesDetailRateBean = new OrgProfileRvSeriesDetailRateDTO();
                rvSeriesDetailRateBean.setCataId(e.getCataId());
                rvSeriesDetailRateBean.setCataName(e.getCataName());
                rvSeriesDetailRateBean.setDimId(e.getDimId());
                rvSeriesDetailRateBean.setDimName(e.getDimName());
                int userCount = null2zero(e.getUserCount());
                rvSeriesDetailRateBean.setUserCount(userCount);
                int achievedUserCount = null2zero(e.getAchievedUserCount());
                rvSeriesDetailRateBean.setAchievedUserCount(achievedUserCount);
                rvSeriesDetailRateBean.setAchievedRate(divide(achievedUserCount, userCount, 4, HALF_UP).doubleValue());

                if (!cataIds.contains(e.getCataId())) {
                    cataIds.add(e.getCataId());
                    OrgProfileRvSeriesDetaiCataDTO bean = new OrgProfileRvSeriesDetaiCataDTO();
                    bean.setCataId(e.getCataId());
                    bean.setCataName(e.getCataName());
                    catas.add(bean);
                }
                rvs.add(rvSeriesDetailRateBean);
            });

            result.setRvs(rvs);
            result.setCatas(catas);
        }
        return result;
    }

    public OrgProfileManagePosJobGradeVO getPositionJobgrade(String orgId, String yearly, String deptId) {
        OrgProfileManagePosJobGradeVO positionJobGradeBean = new OrgProfileManagePosJobGradeVO();
        List<OrgProfileManagePositionJobGradeDTO> jobGrades = new ArrayList<>();
        Map<String, Object> queryParam = new HashMap<>(8);
        queryParam.put(ORG_ID, orgId);
        queryParam.put(YEARLY, yearly);
        queryParam.put(DEPT_ID, isNotBlank(deptId) ? deptId : getRootDeptId(orgId));
        queryParam.put(DELETED, 0);
        SqlParam sqlParam = new SqlParam();
        sqlParam.setOrgId(orgId);
        LinkedHashMap<String, Boolean> orderByFields = new LinkedHashMap<>(8);
        try {
            sqlParam.setSql(buildSqlQuery(DwmDeptPositionJobgrade.class, DWM_DEPT_POSITION_JOBGRADE, queryParam, orderByFields));
            List<DwmDeptPositionJobgrade> positionJobgradeList =
                jsonObject2BeanList(DwmDeptPositionJobgrade.class, spmodelAclService.sql(sqlParam));
            if (CollectionUtils.isNotEmpty(positionJobgradeList)) {
                Map<String, DwmDeptPositionJobgrade> jobGradeMap =
                    StreamUtil.list2map(positionJobgradeList, DwmDeptPositionJobgrade::getJobgradeId);
                Map<String, List<DwmDeptPositionJobgrade>> listMap = positionJobgradeList.stream()
                    .collect(Collectors.groupingBy(DwmDeptPositionJobgrade::getJobgradeId));
                for (Map.Entry<String, List<DwmDeptPositionJobgrade>> entry : listMap.entrySet()) {
                    DwmDeptPositionJobgrade dwmDeptPositionJobgrade = jobGradeMap.get(entry.getKey());
                    if (null != dwmDeptPositionJobgrade) {
                        OrgProfileManagePositionJobGradeDTO jobGradeBean = new OrgProfileManagePositionJobGradeDTO();
                        jobGradeBean.setJobgradeName(dwmDeptPositionJobgrade.getThirdJobgradeName());
                        jobGradeBean.setOrderIndex(dwmDeptPositionJobgrade.getGradeOrder());
                        List<OrgProfileBaseInfoDTO> positions = getOrgProfileBaseInfoDTOS(entry);
                        jobGradeBean.setPositions(positions.stream()
                            .sorted(Comparator.comparing(OrgProfileBaseInfoDTO::getOrderIndex))
                            .collect(Collectors.toList()));
                        if (CollectionUtils.isNotEmpty(jobGradeBean.getPositions())) {
                            jobGrades.add(jobGradeBean);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("LOG61930:getPositionJobgrade error", e);
        }
        positionJobGradeBean.setJobGrades(jobGrades.stream()
            .sorted(Comparator.comparing(OrgProfileManagePositionJobGradeDTO::getOrderIndex))
            .collect(Collectors.toList()));
        return positionJobGradeBean;
    }

    private static @Nonnull List<OrgProfileBaseInfoDTO> getOrgProfileBaseInfoDTOS(
        Map.Entry<String, List<DwmDeptPositionJobgrade>> entry) {
        List<OrgProfileBaseInfoDTO> positions = new ArrayList<>();
        List<DwmDeptPositionJobgrade> positionJobgrades = entry.getValue();
        positionJobgrades.forEach(position -> {
            if (null == position.getManageCount() || 0 == position.getManageCount()) {
                // 只取管理人数大于0的岗位和职级
                return;
            }
            OrgProfileBaseInfoDTO baseInfoBean = new OrgProfileBaseInfoDTO();
            baseInfoBean.setName(position.getThirdPositionName());
            baseInfoBean.setCount(position.getManageCount());
            baseInfoBean.setOrderIndex(position.getPositionOrder());
            positions.add(baseInfoBean);
        });
        return positions;
    }

    public OrgProfileDeptGroupVO getDeptGroup(String orgId, String yearly, String deptId) {
        OrgProfileDeptGroupVO deptGroup4Get = new OrgProfileDeptGroupVO();
        List<OrgProfileDeptGroupDTO> yearDeptGroups = new ArrayList<>();
        Map<String, Object> queryParam = new HashMap<>(8);
        queryParam.put(ORG_ID, orgId);
        queryParam.put(DEPT_ID, isNotBlank(deptId) ? deptId : getRootDeptId(orgId));
        queryParam.put(DELETED, 0);
        queryParam.put(YEARLY, new String[]{yearly, Integer.parseInt(yearly) - 1 + ""});
        SqlParam sqlParam = new SqlParam();
        sqlParam.setOrgId(orgId);
        LinkedHashMap<String, Boolean> orderByFields = new LinkedHashMap<>(8);
        try {
            sqlParam.setSql(buildSqlQuery(DwsDept.class, DWS_DEPT, queryParam, orderByFields));
            List<DwsDept> dwsDepts = jsonObject2BeanList(DwsDept.class, spmodelAclService.sql(sqlParam));

            sqlParam.setSql(buildSqlQuery(DwmDept.class, DWM_DEPT, queryParam, orderByFields));
            List<DwmDept> dwmDepts = jsonObject2BeanList(DwmDept.class, spmodelAclService.sql(sqlParam));

            Map<Integer, DwmDept> dwmDeptMap = CommonUtil.listMap(dwmDepts, DwmDept::getYearly);
            if (CollectionUtils.isNotEmpty(dwsDepts)) {
                Map<Integer, List<DwsDept>> yearlyDwsDeptMap =
                    dwsDepts.stream().collect(Collectors.groupingBy(DwsDept::getYearly));
                for (Map.Entry<Integer, List<DwsDept>> entry : yearlyDwsDeptMap.entrySet()) {
                    List<DwsDept> dwsDeptList = entry.getValue();
                    if (CollectionUtils.isNotEmpty(dwsDeptList)) {
                        DwsDept dwsDept = dwsDeptList.get(0);
                        OrgProfileDeptGroupDTO deptGroupBean = new OrgProfileDeptGroupDTO();
                        BeanHelper.copyProperties(dwsDept, deptGroupBean);
                        DwmDept dwmDept = dwmDeptMap.get(entry.getKey());
                        deptGroupBean.setUserCount(null != dwmDept ? dwmDept.getUserCount() : 0);
                        yearDeptGroups.add(deptGroupBean);
                    }
                }
            }

        } catch (Exception e) {
            log.error("LOG61940:getDeptGroup error", e);
        }
        deptGroup4Get.setYearDeptGroups(yearDeptGroups.stream()
            .sorted(Comparator.comparing(OrgProfileDeptGroupDTO::getYearly).reversed())
            .collect(Collectors.toList()));
        return deptGroup4Get;
    }

    /**
     * 获取指定部门的直属部门数组
     *
     * @param orgId
     * @param parentDeptId
     * @return
     */
    @jakarta.annotation.Nonnull
    private String[] getFirstDeptId(String orgId, String parentDeptId) {
        SqlParam sqlParam = new SqlParam();
        sqlParam.setOrgId(orgId);
        sqlParam.setSql(buildFirstDeptIdQuerySql(orgId, parentDeptId));
        List<DwdDept> depts = jsonObject2BeanList(DwdDept.class, spmodelAclService.sql(sqlParam));
        return depts.stream().map(DwdDept::getDeptId).distinct().toArray(String[]::new);
    }

    public OrgProfileSummaryVO getSummary(String orgId, String yearly, String deptId) {
        OrgProfileSummaryVO orgProfileSummaryVO = new OrgProfileSummaryVO();
        Map<String, Object> queryParams = new HashMap<>(8);
        queryParams.put(YEARLY, yearly);
        queryParams.put(ORG_ID, orgId);
        queryParams.put(DEPT_ID, isNotBlank(deptId) ? deptId : getRootDeptId(orgId));
        queryParams.put(DELETED, 0);

        SqlParam sqlParam = new SqlParam();
        sqlParam.setOrgId(orgId);
        sqlParam.setSql(buildSqlQuery(DwsDept.class, DWS_DEPT, queryParams, null));
        List<DwsDept> depts = jsonObject2BeanList(DwsDept.class, spmodelAclService.sql(sqlParam));
        if (CollectionUtils.isNotEmpty(depts)) {
            DwsDept dwsDept = depts.get(0);
            orgProfileSummaryVO.setManageCount(dwsDept.getManageCount());
            int compCount = null2zero(dwsDept.getCompCount());
            orgProfileSummaryVO.setCompCount(compCount);
            long totalCompUncompCount = compCount + null2zero(dwsDept.getUncompCount());
            orgProfileSummaryVO.setCompetentRate(divide(compCount, totalCompUncompCount, 4, HALF_UP).doubleValue());
        }

        sqlParam.setSql(buildSqlQuery(DwmDept.class, DWM_DEPT, queryParams, null));
        List<DwmDept> dwmDepts = jsonObject2BeanList(DwmDept.class, spmodelAclService.sql(sqlParam));
        if (CollectionUtils.isNotEmpty(dwmDepts)) {
            orgProfileSummaryVO.setUserCount(null2zero(dwmDepts.get(0).getUserCount()));
        }

        int compCount = null2zero(orgProfileSummaryVO.getCompCount());
        int userCount = null2zero(orgProfileSummaryVO.getUserCount());
        orgProfileSummaryVO.setCompetentRate(divide(compCount, userCount, 4, HALF_UP).doubleValue()
        );
        return orgProfileSummaryVO;
    }

    /**
     * 获取团队画像中男女性别占比数统计
     *
     * @param orgId
     * @return
     */
    public OrgProfileSexInfoDTO getTeamProfileSexRatio(String orgId, String deptId) {
        OrgProfileSexInfoDTO orgProfileSexInfo = new OrgProfileSexInfoDTO();
        List<String> deptIds = getDeptIdIncludeChildren(orgId, deptId);

        SqlParam sqlParam = new SqlParam();
        sqlParam.setSql(buildTeamProfileSexRatioQuerySql(orgId, deptIds));
        sqlParam.setOrgId(orgId);
        List<JSONObject> results = new ArrayList<>();
        try {
            results = spmodelAclService.sql(sqlParam);
        } catch (Exception e) {
            log.error("LOG65220:", e);
        }
        if (CollectionUtils.isEmpty(results)) {
            log.info("LOG65230:");
            return orgProfileSexInfo;
        }
        JSONObject jsonObject = results.get(0);
        int maleCount = null2zero(jsonObject.getInteger("maleCount"));
        int femaleCount = null2zero(jsonObject.getInteger("femaleCount"));
        // 防止Integer为null时转long报错,这里处理一下
        orgProfileSexInfo.setMaleCount(maleCount);
        orgProfileSexInfo.setFemaleCount(femaleCount);
        orgProfileSexInfo.setUserCnt(maleCount + femaleCount);
        return orgProfileSexInfo;
    }

    @jakarta.annotation.Nonnull
    private List<String> getDeptIdIncludeChildren(String orgId, String deptId) {
        Optional<Dept> udpDept = deptDomainRepo.load(orgId, deptId, Dept.LoadConfig.WITH_ALL);
        List<String> deptIds = udpDept.map(Dept::extractDeptIdIncludeDescendants)
            .orElseThrow(() -> new ApiException(ExceptionKeys.DEPT_NOT_EXIST));
        Validate.isNotEmpty(deptIds, ExceptionKeys.DEPT_NOT_EXIST);
        return new ArrayList<>(distinct(filterNullAndBlank(deptIds)));
    }

    public GenericFileExportVO exportUserDimResults(String orgId, String deptId) {
        List<String> deptIds = getDeptIdIncludeChildren(orgId, deptId);

        String lockKey = String.format(RedisKeys.LK_ORG_PROFILE_RV_DETAIL_EXPT, orgId, deptId);
        try {
            if (!lockService.tryLock(lockKey, MAX_LEASE_TIME, TimeUnit.SECONDS)) {
                log.info("LOG13595:{}", lockKey);
                return new GenericFileExportVO();
            }
            return doExportRvSeriesDetail(orgId, deptIds);
        } finally {
            lockService.unLock(lockKey);
        }
    }

    private GenericFileExportVO doExportRvSeriesDetail(String orgId, List<String> deptIds) {
        String sheet1Id = "sheet1";
        String sheet2Id = "sheet2";

        // 准备导出的数据,并验证
        List<Object> sheet1data = initSheet1Data(orgId, deptIds);
        List<Object> sheet2data = initSheet2Data(orgId, deptIds);
        Map<String, List<Object>> multiSheetDatas = new LinkedHashMap<>(8);
        multiSheetDatas.put(sheet1Id, sheet1data);
        multiSheetDatas.put(sheet2Id, sheet2data);

        // 准备多sheet的名称
        List<IdName> multiSheetNames = new ArrayList<>();
        String sheet1Name = i18nComponent.getI18nValue(SHEET_1_NAME);
        multiSheetNames.add(new IdName(sheet1Id, sheet1Name));
        String sheet2Name = i18nComponent.getI18nValue(SHEET_2_NAME);
        multiSheetNames.add(new IdName(sheet2Id, sheet2Name));

        // 准备多sheet的表头
        Map<String, List<List<String>>> multiSheetHeaders = new LinkedHashMap<>(8);
        multiSheetHeaders.put(sheet1Id, getInitHeader(SHEET_1_HEADER_KEYS, SHEET_1_HEADER_PREFIX));
        multiSheetHeaders.put(sheet2Id, getInitHeader(SHEET_2_HEADER_KEYS, SHEET_2_HEADER_PREFIX));

        DynamicExcelExportContent exportData = new DynamicExcelExportContent();
        exportData.setHeaders(multiSheetHeaders);
        exportData.setData(multiSheetDatas);
        exportData.setSheets(multiSheetNames);
        String fileName = i18nComponent.getI18nValue("apis.sptalentrv.team.rv.detail.export.file.name") + "_" +
                          DateUtil.format(new Date(), "yyyyMMddHHmmss") + FileConstants.FILE_SUFFIX_XLSX;
        long taskId = dlcComponent.prepareExport(fileName, teamDmpUserDimResultExportStrategy);
        String path = dlcComponent.upload2Disk(fileName, exportData, teamDmpUserDimResultExportStrategy, taskId);

        log.info("LOG66170:{}", path);
        return GenericFileExportVO.byFilePath(path);
    }

    private List<Object> initSheet2Data(String orgId, List<String> deptIds) {
        // 获取学员达标明细
        List<DwdUserRvDetail> userRvDetails = getUserRvDetail(orgId, deptIds);

        List<Object> rowData = new ArrayList<>();
        for (DwdUserRvDetail rvDetail : userRvDetails) {
            List<Object> cellVal = new ArrayList<>();
            rowData.add(cellVal);
            cellVal.add(Optional.ofNullable(rvDetail.getCataName()).orElse(EMPTY));
            cellVal.add(Optional.ofNullable(rvDetail.getDimName()).orElse(EMPTY));
            cellVal.add(Optional.ofNullable(rvDetail.getFullname()).orElse(EMPTY));
            cellVal.add(Optional.ofNullable(rvDetail.getUsername()).orElse(EMPTY));
            cellVal.add(rvDetail.getAchieved() == null || rvDetail.getAchieved() == 0 ? "不达标" : "达标");
        }
        if (CollectionUtils.isEmpty(rowData)) {
            List<Object> cellVal = new ArrayList<>();
            rowData.add(cellVal);
            cellVal.add("");
            cellVal.add("");
            cellVal.add("");
            cellVal.add("");
            cellVal.add("");
        }
        return rowData;
    }

    @jakarta.annotation.Nonnull
    private List<Object> initSheet1Data(String orgId, List<String> deptIds) {
        // 获取维度达标率
        List<DwmDeptRvDetail> deptRvDetail = getDeptRvDetail(orgId, deptIds);

        List<Object> rowData = new ArrayList<>();
        for (DwmDeptRvDetail dwmDeptRvDetail : deptRvDetail) {
            List<Object> cellVal = new ArrayList<>();
            rowData.add(cellVal);
            cellVal.add(Optional.ofNullable(dwmDeptRvDetail.getCataName()).orElse(EMPTY));
            cellVal.add(Optional.ofNullable(dwmDeptRvDetail.getDimName()).orElse(EMPTY));
            cellVal.add(Optional.ofNullable(dwmDeptRvDetail.getAchievedUserCount()).orElse(0));
            cellVal.add(calcUnAchievedUserCount(dwmDeptRvDetail));
            cellVal.add(calcAchievedRate(dwmDeptRvDetail));
        }
        if (CollectionUtils.isEmpty(rowData)) {
            List<Object> cellVal = new ArrayList<>();
            rowData.add(cellVal);
            cellVal.add("");
            cellVal.add("");
            cellVal.add("");
            cellVal.add("");
            cellVal.add("");
        }
        return rowData;
    }

    private static int calcUnAchievedUserCount(DwmDeptRvDetail dwmDeptRvDetail) {
        Integer userCount = dwmDeptRvDetail.getUserCount();
        if (userCount == null || userCount == 0) {
            return 0;
        }
        Integer achievedUserCount = dwmDeptRvDetail.getAchievedUserCount();
        if (achievedUserCount == null) {
            achievedUserCount = 0;
        }
        return Math.max(0, userCount - achievedUserCount);
    }

    @jakarta.annotation.Nonnull
    private List<List<String>> getInitHeader(String[] headerKeys, String headerPrefix) {
        Locale locale = authService.getLocale();
        List<List<String>> sheet1Headers = new ArrayList<>();
        Arrays.stream(headerKeys).forEach(key -> {
            String cellHeader = ApiUtil.getL10nString(messageSourceService, headerPrefix + key, locale);
            List<String> cellHeaderList = new ArrayList<>();
            cellHeaderList.add(cellHeader);
            sheet1Headers.add(cellHeaderList);
        });
        return sheet1Headers;
    }

    @jakarta.annotation.Nonnull
    private String calcAchievedRate(DwmDeptRvDetail dwmDeptRvDetail) {
        Integer userCount = dwmDeptRvDetail.getUserCount();
        if (userCount == null || userCount == 0) {
            return "0%";
        }
        Integer achievedUserCount = dwmDeptRvDetail.getAchievedUserCount();
        BigDecimal rate = dividePer(null2zero(achievedUserCount), userCount, 2);
        // 格式化为不带小数点的百分比字符串,
        return rate.toBigInteger() + "%";
    }

    @Nonnull
    private List<DwdUserRvDetail> getUserRvDetail(String orgId, List<String> deptIds) {
        SqlParam sqlParam = new SqlParam();
        sqlParam.setOrgId(orgId);
        sqlParam.setSql(buildUserRvDetailQuerySql(orgId, deptIds));
        @Nonnull List<DwdUserRvDetail> dataList =
            jsonObject2BeanList(DwdUserRvDetail.class, spmodelAclService.sql(sqlParam));
        // 填充账号和姓名
        fillUserAndFullName(orgId, dataList);
        return dataList;
    }

    private void fillUserAndFullName(@Nonnull String orgId, @Nonnull List<DwdUserRvDetail> records) {
        List<String> UserIds = StreamUtil.mapList(records, DwdUserRvDetail::getUserId);
        List<UdpLiteUserPO> udpLiteUserPos = udpLiteUserMapper.selectByUserIds(orgId, UserIds);
        for (DwdUserRvDetail data : records) {
            for (UdpLiteUserPO udpLiteUserPo : udpLiteUserPos) {
                if (Objects.equals(udpLiteUserPo.getId(), data.getUserId())) {
                    data.setUsername(udpLiteUserPo.getUsername());
                    data.setFullname(udpLiteUserPo.getFullname());
                    break;
                }
            }
        }
    }

    private List<DwmDeptRvDetail> getDeptRvDetail(String orgId, List<String> deptIds) {
        SqlParam sqlParam = new SqlParam();
        sqlParam.setOrgId(orgId);
        sqlParam.setSql(buildDeptRvDetailQuerySql(orgId, deptIds));
        return jsonObject2BeanList(DwmDeptRvDetail.class, spmodelAclService.sql(sqlParam));
    }

    /**
     * 查询指定部门的平均年龄和平均司龄
     *
     * @param orgId
     * @param deptId
     * @return
     */
    @Nonnull
    public TeamOrgProfileBasicClientVO getTeamAvgAgeAndServiceYears(String orgId, String deptId) {
        List<String> deptIds = getDeptIdIncludeChildren(orgId, deptId);
        SqlParam sqlParam = new SqlParam();
        sqlParam.setOrgId(orgId);
        sqlParam.setSql(buildTeamAvgAgeAndServiceYearsQuerySql(orgId, deptIds));
        List<JSONObject> datas = spmodelAclService.sql(sqlParam);
        if (CollectionUtils.isEmpty(datas)) {
            return new TeamOrgProfileBasicClientVO();
        }
        TeamOrgProfileBasicClientVO result =
            jsonObject2Bean(TeamOrgProfileBasicClientVO.class, datas.iterator().next());
        return result == null ? new TeamOrgProfileBasicClientVO() : result;
    }

    public OrgProfileRvSeriesDmpVO getTeamRvSeriesDetail(String orgId, String deptId) {
        List<String> deptIds = getDeptIdIncludeChildren(orgId, deptId);
        SqlParam sqlParam = new SqlParam();
        sqlParam.setOrgId(orgId);
        sqlParam.setSql(buildDeptRvDetailQuerySql(orgId, deptIds));
        List<JSONObject> datas = spmodelAclService.sql(sqlParam);
        if (CollectionUtils.isEmpty(datas)) {
            return new OrgProfileRvSeriesDmpVO();
        }
        List<OrgProfileRvSeriesDetailRateDTO> rvs = jsonObject2BeanList(OrgProfileRvSeriesDetailRateDTO.class, datas);
        // 计算达标率
        for (OrgProfileRvSeriesDetailRateDTO rv : rvs) {
            // Use MathUtil for division
            rv.setAchievedRate(divide(null2zero(rv.getAchievedUserCount()), null2zero(rv.getUserCount()), 4, HALF_UP).doubleValue());
        }
        // 提取不重复的分类id和name
        List<OrgProfileRvSeriesDetaiCataDTO> catas = rvs.stream().map(e -> {
            OrgProfileRvSeriesDetaiCataDTO bean = new OrgProfileRvSeriesDetaiCataDTO();
            bean.setCataId(e.getCataId());
            bean.setCataName(e.getCataName());
            return bean;
        }).distinct().collect(Collectors.toList());
        OrgProfileRvSeriesDmpVO result = new OrgProfileRvSeriesDmpVO();
        result.setRvs(rvs);
        result.setCatas(catas);
        return result;
    }

    public OrgProfileSkillMatrixVO findSkillMatrix(String orgId, String deptId, String yearly) {
        OrgProfileSkillMatrixVO res = new OrgProfileSkillMatrixVO();
        // 获取部门人数
        Map<String, Object> queryParams = new HashMap<>(8);
        queryParams.put(YEARLY, yearly);
        queryParams.put(ORG_ID, orgId);
        queryParams.put(DELETED, 0);
        queryParams.put(DEPT_ID, isNotBlank(deptId) ? deptId : getRootDeptId(orgId));
        SqlParam sqlParam = new SqlParam();
        sqlParam.setOrgId(orgId);
        sqlParam.setSql(buildSqlQuery(DwmDept.class, DWM_DEPT, queryParams, null));
        log.info("LOG14465:findSkillMatrix sqlParam ={}", BeanHelper.bean2Json(sqlParam));
        List<DwmDept> dwmDepts = jsonObject2BeanList(DwmDept.class, spmodelAclService.sql(sqlParam));
        log.info("LOG14455:findSkillMatrix dwmDepts={}", BeanHelper.bean2Json(dwmDepts));
        if (CollectionUtils.isEmpty(dwmDepts)) {
            return new OrgProfileSkillMatrixVO();
        }

        DwmDept dwmDept = dwmDepts.get(0);
        Integer userCount = dwmDept.getUserCount();
        if (userCount == null || userCount == 0) {
            log.debug("LOG20553:orgId={},deptId={}", orgId, deptId);
            return res;
        }

        // 获取部门下 拥有单个能力拥有超过三个人数的记录,并且,该能力下的人数，大于等于部门总人数的20%
        SqlParam sqlParam1 = new SqlParam();
        sqlParam1.setOrgId(orgId);
        sqlParam1.setSql(buildEvalSkillQuery4Matrix(orgId, deptId));
        log.info("LOG14445:findSkillMatrix sqlParam1={}", BeanHelper.bean2Json(sqlParam1));
        List<DwdDeptEvelSkillRt> skillRts =
            jsonObject2BeanList(DwdDeptEvelSkillRt.class, spmodelAclService.sql(sqlParam1));
        log.info("LOG14435:findSkillMatrix skillRts={}", BeanHelper.bean2Json(skillRts));
        if (CollectionUtils.isEmpty(skillRts)) {
            new OrgProfileSkillMatrixVO();
        }
        BigDecimal deptUserNum = new BigDecimal(userCount);
        List<DwdDeptEvelSkillRt> results = new ArrayList<>();
        for (DwdDeptEvelSkillRt skillRt : skillRts) {
            Integer skillUserCnt = skillRt.getSkillUserCnt();
            if (skillUserCnt == null) {
                continue;
            }
            BigDecimal skillUserNum = new BigDecimal(skillUserCnt);
            BigDecimal divide = skillUserNum.divide(deptUserNum, 4, HALF_UP);
            if (divide.compareTo(new BigDecimal("0.2")) < 0) {
                continue;
            }
            results.add(skillRt);
        }

        if (CollectionUtils.isEmpty(results)) {
            return res;
        }
        List<OrgProfileSkillScoreVO> skillScoresList = new ArrayList<>();
        BigDecimal xAvgSum = BigDecimal.ZERO;
        BigDecimal yAvgSum = BigDecimal.ZERO;
        for (DwdDeptEvelSkillRt result : results) {
            OrgProfileSkillScoreVO skillScore = new OrgProfileSkillScoreVO();
            skillScore.setSkillId(result.getSkillId());
            skillScore.setSkillName(result.getSkillName());
            skillScore.setAvgScore(result.getTenAvgScore());
            skillScore.setSpreadScore(result.getSkillSpread());
            skillScoresList.add(skillScore);
            xAvgSum = xAvgSum.add(result.getTenAvgScore());
            yAvgSum = yAvgSum.add(result.getSkillSpread());
        }
        res.setSkillScoresList(skillScoresList);
        res.setAvgScorePart(xAvgSum.divide(new BigDecimal(skillScoresList.size()), 2, HALF_UP));
        res.setSpreadScorePart(yAvgSum.divide(new BigDecimal(skillScoresList.size()), 2, HALF_UP));
        return res;
    }

    /**
     * 获取部门聚合数据
     *
     * @param orgId
     * @param deptId
     * @return
     */
    public Optional<DwmDept> getDwmDeptInfo(@NonNull String orgId, @Nullable String deptId) {
        if (isBlank(deptId)) {
            log.warn("LOG14115:");
            return Optional.empty();
        }
        Map<String, Object> queryParams = new HashMap<>(8);
        queryParams.put(YEARLY, DateTimeUtil.getNowYear());
        queryParams.put(ORG_ID, orgId);
        queryParams.put(DELETED, 0);
        queryParams.put(DEPT_ID, deptId);

        // 获取
        SqlParam sqlParam = new SqlParam();
        sqlParam.setOrgId(orgId);
        sqlParam.setSql(buildSqlQuery(DwmDept.class, DWM_DEPT, queryParams, null));
        List<DwmDept> dwmDepts = jsonObject2BeanList(DwmDept.class, spmodelAclService.sql(sqlParam));
        if (CollectionUtils.isEmpty(dwmDepts)) {
            return Optional.empty();
        }
        return Optional.of(dwmDepts.iterator().next());
    }

    public List<UserDimGrid4Info> getUserDimGrids(String orgId, OrgProfileUserDimGridQuery criteria) {
        String xDimId = criteria.getXDimId();
        String yDimId = criteria.getYDimId();
        String querySql = buildPrjDimGridQuerySql(orgId, asList(xDimId, yDimId), criteria.getScopeDeptIds());
        List<PrjUserDetailDTO> prjUserDetailDtos = queryListFromModel(orgId, querySql, PrjUserDetailDTO.class);
        if (CollectionUtils.isEmpty(prjUserDetailDtos)) {
            return Collections.emptyList();
        }
        Map<String, List<PrjUserDetailDTO>> userPrjMap =
            prjUserDetailDtos.stream().collect(groupingBy(PrjUserDetailDTO::getUserId));

        Set<String> userIds = userPrjMap.keySet();
        List<UdpLiteUserPO> udpLiteUsers = udpLiteUserMapper.selectByUserIds(orgId, userIds);
        Map<String, UdpLiteUserPO> userIdUserMap = list2map(udpLiteUsers, UdpLiteUserPO::getId);

        Iterator<Map.Entry<String, List<PrjUserDetailDTO>>> iterator = userPrjMap.entrySet().iterator();
        List<UserDimGrid4Info> datas = new ArrayList<>();
        while (iterator.hasNext()) {
            Map.Entry<String, List<PrjUserDetailDTO>> entry = iterator.next();
            String userId = entry.getKey();
            List<PrjUserDetailDTO> prjDetails = entry.getValue();
            if (prjDetails.size() < 2 || !containsAllDim(prjDetails, xDimId, yDimId) || !userIdUserMap.containsKey(userId)) {
                log.warn("LOG60590:{}", "删除不符合条件的数据");
                iterator.remove();
                continue;
            }

            UserDimGrid4Info userDimGrid4Info = new UserDimGrid4Info();
            datas.add(userDimGrid4Info);

            userDimGrid4Info.setXValue(getValue(prjDetails, xDimId));
            userDimGrid4Info.setXDimId(xDimId);
            userDimGrid4Info.setYValue(getValue(prjDetails, yDimId));
            userDimGrid4Info.setYDimId(yDimId);
            UdpLiteUserPO udpLiteUser = userIdUserMap.get(userId);
            if (udpLiteUser != null) {
                userDimGrid4Info.setFullname(udpLiteUser.getFullname());
                userDimGrid4Info.setUserId(udpLiteUser.getId());
                userDimGrid4Info.setImgUrl(udpLiteUser.getImgUrl());
            }
        }
        return datas;
    }

    /**
     * 从数据集获取用户维度详情
     *
     * @param orgId  组织ID
     * @param userId 用户ID
     * @param gridId 宫格ID
     * @return 用户维度详情
     */
    @Nullable
    public XpdResultUserDimDetailVO getUserDimDetailFromDataset(String orgId, String userId, String gridId) {
        // 从业务库获取用户详细信息
        UdpLiteUserPO targetUser = udpLiteUserMapper.selectByUserIdWithDeleted(orgId, userId);
        if (targetUser == null) {
            log.warn("LOG20334:用户不存在, orgId={}, userId={}", orgId, userId);
            return null;
        }

        // 使用宫格配置的类型查询用户维度数据
        XpdGridPO xpdGrid = xpdGridMapper.selectByPrimaryKey(gridId);
        Validate.isNotNull(xpdGrid, ExceptionKeys.XPD_GRID_NOT_FOUND);
        String sql = SpmodelSqlBuilder.buildUserDimensionDataSql(orgId, Collections.singletonList(userId), xpdGrid.getGridType());
        if (StringUtils.isBlank(sql)) {
            log.warn("LOG20335:构建用户维度数据SQL失败, orgId={}, userId={}", orgId, userId);
            return null;
        }

        SqlParam param = new SqlParam();
        param.setOrgId(orgId);
        param.setSql(sql);

        log.info("LOG20336:查询用户维度数据, orgId={}, userId={}, sql={}", orgId, userId, sql);

        // 查询用户维度数据
        List<UserDimensionDataDTO> userDimensions;
        try {
            userDimensions = jsonObject2BeanList(UserDimensionDataDTO.class, spmodelAclService.sql(param));
        } catch (Exception e) {
            log.error("LOG20337:查询用户维度数据失败, orgId={}, userId={}", orgId, userId, e);
            return null;
        }

        if (CollectionUtils.isEmpty(userDimensions)) {
            log.warn("LOG20338:用户维度数据为空, orgId={}, userId={}", orgId, userId);
            return null;
        }

        // 转换为用户维度等级信息
        List<XpdResultUserDimLevelVO> userDimLevels = userDimensions.stream()
            .map(dimData -> {
                XpdResultUserDimLevelVO levelVO = new XpdResultUserDimLevelVO();
                levelVO.setSdDimId(dimData.getDimensionId());
                levelVO.setSdDimName(dimData.getDimensionName());
                levelVO.setOrderIndex(dimData.getCalibrationLevel());
                return levelVO;
            })
            .collect(Collectors.toList());

        // 构建返回结果
        XpdResultUserDimDetailVO result = new XpdResultUserDimDetailVO(targetUser);
        result.setUserDimLevels(userDimLevels);

        // 获取宫格层级信息
        List<XpdGridLevelVO> gridLevels = getGridLevels(orgId, xpdGrid);
        result.setGridLevels(gridLevels);

        return result;
    }

    /**
     * 获取宫格层级信息
     *
     * @param orgId   组织ID
     * @param xpdGrid 宫格配置
     * @return 宫格层级信息列表
     */
    private List<XpdGridLevelVO> getGridLevels(String orgId, XpdGridPO xpdGrid) {
        List<XpdGridLevelVO> gridLevels = new ArrayList<>();

        try {
            // 从数据库获取宫格分层信息
            List<XpdGridLevelPO> xpdGridLevels = xpdGridLevelMapper.listByGridId(orgId, xpdGrid.getId());
            if (CollectionUtils.isNotEmpty(xpdGridLevels)) {
                // 转换为VO对象
                gridLevels = XpdGridLevelVO.Assembler.INSTANCE.toXpdGridLevelVos(xpdGridLevels);

                // 设置第三维度颜色
                for (int i = 0; i < gridLevels.size() && i < xpdGridLevels.size(); i++) {
                    XpdGridLevelVO gridLevel = gridLevels.get(i);
                    XpdGridLevelPO gridLevelPO = xpdGridLevels.get(i);
                    // 设置第三维度颜色
                    gridLevel.setThirdDimColor(gridLevelPO.decideThirdDimColor(xpdGrid, false, appProperties));
                }
            } else {
                log.warn("LOG20342:宫格分层信息为空, orgId={}, gridId={}", orgId, xpdGrid.getId());
            }
        } catch (Exception e) {
            log.error("LOG20343:获取宫格分层信息失败, orgId={}, gridId={}", orgId, xpdGrid.getId(), e);
        }

        return gridLevels;
    }

    private int getValue(List<PrjUserDetailDTO> prjDetails, String targetDimId) {
        PrjUserDetailDTO prjUserDetailDTO = prjDetails.stream()
            .filter(e -> Objects.equals(e.getDimensionId(), targetDimId))
            .findFirst()
            .orElseThrow(() -> new ApiException("维度[" + targetDimId + "]不存在明细"));
        return prjUserDetailDTO.getCalibrationLevel();
    }

    private boolean containsAllDim(List<PrjUserDetailDTO> prjDetails, String xDimId, String yDimId) {
        Set<String> dimIds = StreamUtil.map2set(prjDetails, PrjUserDetailDTO::getDimensionId);
        return dimIds.containsAll(asList(xDimId, yDimId));
    }

}
