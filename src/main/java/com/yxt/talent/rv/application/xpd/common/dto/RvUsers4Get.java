package com.yxt.talent.rv.application.xpd.common.dto;

import java.lang.String;
import java.util.Date;
import com.yxt.modelhub.api.bean.dto.AmUser4DTO;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.lang.Integer;
import java.math.BigDecimal;
import com.yxt.modelhub.api.bean.dto.AmSlDrawer4RespDTO;
import lombok.Getter;
import lombok.Setter;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
@Getter
@Setter
@Schema(description = "盘点人员详情返回")
public class RvUsers4Get implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description="主键;id主键")
    private String id;
    @Schema(description="机构号;机构id")
    private String orgId;
    @Schema(description="名称")
    private String name;
    @Schema(description="创建时间")
    private Date createTime;
    @Schema(description="更新时间")
    private Date updateTime;
    @Schema(description="创建人;创建人id")
    @JsonProperty("@createUserId")
    private AmUser4DTO createUserId;
    @Schema(description="更新人;更新人id")
    @JsonProperty("@updateUserId")
    private AmUser4DTO updateUserId;
    @Schema(description="盘点人员;按照业务需求,返回应用的实体字段")
    @JsonProperty("@userids")
    private AmUser4DTO userids;
    @Schema(description="盘点进度")
    private Integer projschedule;
    @Schema(description="盘点结果类型")
    private String resulttype;
    @Schema(description="得分")
    private BigDecimal scorevalue;
    @Schema(description="达标率")
    private Integer qualifiedptg;
    @Schema(description="盘点结果;按照业务需求,返回应用的实体字段")
    @JsonProperty("@xpdlevelid")
    private AmSlDrawer4RespDTO xpdlevelid;
    @Schema(description="是否胜任")
    private Integer competent;
    @Schema(description="发展建议")
    private String suggestion;
}
