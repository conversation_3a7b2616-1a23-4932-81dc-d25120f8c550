package com.yxt.talent.rv.application.xpd.result.strategy.userdimcombresult;

import com.yxt.talent.rv.model.calimeet.CaliDimResultDto;
import com.yxt.talent.rv.model.xpd.*;
import com.yxt.talent.rv.model.xpd.mapper.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import jakarta.annotation.Nullable;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 默认的用户维度组结果计算策略
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DefaultUserDimCombResultStrategy implements UserDimCombResultStrategy {

    private final XpdGridMapper xpdGridMapper;
    private final XpdGridLevelMapper xpdGridLevelMapper;
    private final XpdGridCellMapper xpdGridCellMapper;
    private final XpdResultUserDimcombMapper xpdResultUserDimcombMapper; // 假设用于更新或检查现有结果，如果不需要可以移除

    @Override
    public List<XpdResultUserDimcombPO> calculateUserDimCombResults(
            String orgId, String xpdId, String userId, List<XpdDimCombPO> dimCombs,
            Map<String, CaliDimResultDto> dimResultMap, String operatorId) {

        List<XpdResultUserDimcombPO> resultList = new ArrayList<>();

        // 获取宫格配置信息
        XpdGridPO xpdGrid = getXpdGrid(orgId, xpdId);
        if (xpdGrid == null) {
            log.warn("LOG21683:项目{}没有宫格配置，无法计算维度组结果", xpdId);
            return resultList;
        }

        // 获取宫格分层信息，用于坐标映射
        List<XpdGridLevelPO> gridLevels = xpdGridLevelMapper.listByGridId(orgId, xpdGrid.getId());
        Map<String, Integer> gridLevelOrderMap = gridLevels.stream()
            .collect(Collectors.toMap(XpdGridLevelPO::getId, XpdGridLevelPO::getOrderIndex));

        // 获取格子信息
        List<XpdGridCellPO> gridCells = xpdGridCellMapper.listByGridId(orgId, xpdGrid.getId());
        Map<String, XpdGridCellPO> gridCellMap = buildGridCellMap(gridCells, xpdGrid.getConfigType());

        // 遍历每个维度组，计算用户在该维度组中的落位
        for (XpdDimCombPO dimComb : dimCombs) {
            XpdResultUserDimcombPO combResult = calculateSingleDimCombResult(
                orgId, xpdId, userId, dimComb, dimResultMap, gridLevelOrderMap,
                gridCellMap, xpdGrid.getConfigType(), operatorId);

            if (combResult != null) {
                resultList.add(combResult);
            }
        }
        return resultList;
    }

    /**
     * 获取项目的宫格配置
     */
    @Nullable
    private XpdGridPO getXpdGrid(String orgId, String xpdId) {
        return xpdGridMapper.selectByXpdId(orgId, xpdId);
    }

    /**
     * 构建格子映射表
     */
    private Map<String, XpdGridCellPO> buildGridCellMap(List<XpdGridCellPO> gridCells, Integer configType) {
        return gridCells.stream().collect(Collectors.toMap(
            cell -> buildCellKey(cell.getXIndex(), cell.getYIndex(), cell.getDimCombId(), configType),
            Function.identity()
        ));
    }

    /**
     * 构建格子key
     */
    private String buildCellKey(Integer xIndex, Integer yIndex, String dimCombId, Integer configType) {
        return (configType != null && configType == 0) ?
            xIndex + "_" + yIndex :
            dimCombId + "_" + xIndex + "_" + yIndex;
    }

    /**
     * 计算单个维度组的结果
     */
    @Nullable
    private XpdResultUserDimcombPO calculateSingleDimCombResult(
            String orgId, String xpdId, String userId, XpdDimCombPO dimComb,
            Map<String, CaliDimResultDto> dimResultMap, Map<String, Integer> gridLevelOrderMap,
            Map<String, XpdGridCellPO> gridCellMap, Integer configType, String operatorId) {

        // 获取X轴和Y轴维度的结果
        CaliDimResultDto xDimResult = dimResultMap.get(dimComb.getXSdDimId());
        CaliDimResultDto yDimResult = dimResultMap.get(dimComb.getYSdDimId());

        if (xDimResult == null || yDimResult == null) {
            log.debug("LOG21693:用户{}在维度组{}中缺少维度结果，X轴维度{}结果:{}, Y轴维度{}结果:{}",
                userId, dimComb.getId(), dimComb.getXSdDimId(), xDimResult != null,
                dimComb.getYSdDimId(), yDimResult != null);
            return null;
        }

        // 获取X和Y坐标
        Integer xIndex = gridLevelOrderMap.get(xDimResult.getGridLevelId());
        Integer yIndex = gridLevelOrderMap.get(yDimResult.getGridLevelId());

        if (xIndex == null || yIndex == null) {
            log.warn("LOG21703:用户{}在维度组{}中无法获取坐标，X轴分层{}坐标:{}, Y轴分层{}坐标:{}",
                userId, dimComb.getId(), xDimResult.getGridLevelId(), xIndex,
                yDimResult.getGridLevelId(), yIndex);
            return null;
        }

        // 根据坐标获取格子
        String cellKey = buildCellKey(xIndex, yIndex, dimComb.getId(), configType);
        XpdGridCellPO gridCell = gridCellMap.get(cellKey);

        if (gridCell == null) {
            log.warn("LOG21713:用户{}在维度组{}中无法找到格子，坐标({},{})，key:{}",
                userId, dimComb.getId(), xIndex, yIndex, cellKey);
            return null;
        }

        // 构建结果对象
        XpdResultUserDimcombPO result = new XpdResultUserDimcombPO();
        result.setOrgId(orgId);
        result.setXpdId(xpdId);
        result.setUserId(userId);
        result.setDimCombId(dimComb.getId());
        result.setGridCellId(gridCell.getId());
        result.setXpdLevelId(gridCell.getXpdLevelId());
        result.setCreatedBy(operatorId);
        result.setUpdatedBy(operatorId);

        return result;
    }
}
