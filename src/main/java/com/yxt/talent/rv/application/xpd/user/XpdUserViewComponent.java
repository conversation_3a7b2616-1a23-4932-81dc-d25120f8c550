package com.yxt.talent.rv.application.xpd.user;

import com.alibaba.fastjson.JSON;
import com.yxt.aom.base.entity.arrange.ActivityArrangeItem;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.common.util.StreamUtil;
import com.yxt.common.util.Validate;
import com.yxt.modelhub.api.bean.dto.SearchDTO;
import com.yxt.modelhub.api.utils.QueryUtil;
import com.yxt.spsdfacade.bean.spsd.DimensionList4Get;
import com.yxt.spsdfacade.bean.spsd.IndicatorDto;
import com.yxt.spsdfacade.bean.spsd.ModelBase4Facade;
import com.yxt.spsdfacade.bean.spsd.ModelInfo;
import com.yxt.spsdfacade.service.SptalentsdFacade;
import com.yxt.talent.rv.application.xpd.common.dto.ActivityInfo4UserDto;
import com.yxt.talent.rv.application.xpd.common.dto.DimUserIndicatorResultDto;
import com.yxt.talent.rv.application.xpd.common.dto.RvUsers4Get;
import com.yxt.talent.rv.application.xpd.result.strategy.userdimresult.UserDimResultFillContext;
import com.yxt.talent.rv.application.xpd.xpd.XpdService;
import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdUserDimResultsVO;
import com.yxt.talent.rv.controller.manage.xpd.rule.enums.DimRuleCalcRefEnum;
import com.yxt.talent.rv.controller.manage.xpd.user.command.ViewCell4UserCmd;
import com.yxt.talent.rv.controller.manage.xpd.user.dto.UserCellIndexDTO;
import com.yxt.talent.rv.controller.manage.xpd.user.dto.ViewUserDimDTO;
import com.yxt.talent.rv.controller.manage.xpd.user.dto.XpdUserLevelNumDto;
import com.yxt.talent.rv.controller.manage.xpd.user.viewobj.*;
import com.yxt.talent.rv.controller.openapi.viewobj.PerfGradeVO;
import com.yxt.talent.rv.infrastructure.common.utilities.util.ApassEntityUtils;
import com.yxt.talent.rv.infrastructure.common.utilities.util.MathUtil;
import com.yxt.talent.rv.infrastructure.config.AppProperties;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.activity.ActivityPerfMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.aom.RvActivityArrangeItemMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.perf.PerfGradeMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.perf.PerfMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.perf.PerfPeriodMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.aom.ActivityMemberStatistics;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.activity.ActivityPerfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfGradePO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.perf.PerfPeriodPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.*;
import com.yxt.talent.rv.infrastructure.service.remote.SpsdAclService;
import com.yxt.talent.rv.infrastructure.service.userinfo.UserBaseInfo;
import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class XpdUserViewComponent {
    private final UdpLiteUserMapper udpLiteUserMapper;
    private final XpdMapper xpdMapper;
    private final XpdRuleMapper ruleMapper;
    private final XpdResultUserMapper resultUserMapper;
    private final XpdResultUserDimMapper resultUserDimMapper;
    private final XpdLevelMapper xpdLevelMapper;
    private final XpdDimMapper dimMapper;
    private final SpsdAclService spsdAclService;
    private final XpdGridLevelMapper gridLevelMapper;
    private final XpdImportMapper importMapper;
    private final XpdGridCellMapper gridCellMapper;
    private final XpdGridMapper gridMapper;
    private final XpdActivityMemberStatisticsMapper memberStatisticsMapper;
    private final XpdResultUserIndicatorMapper resultUserIndicatorMapper;
    private final XpdDimRuleMapper dimRuleMapper;
    private final XpdDimRuleCalcMapper dimRuleCalcMapper;
    private final RvActivityArrangeItemMapper rvActivityArrangeItemMapper;
    // rv_xpd_result_indicator
    private final XpdResultIndicatorMapper resultIndicatorMapper;
    private final PerfMapper perfMapper;
    private final PerfPeriodMapper perfPeriodMapper;
    private final PerfGradeMapper perfGradeMapper;
    private final XpdService xpdService;
    private final ActivityPerfMapper activityPerfMapper;
    private final XpdDimCombMapper xpdDimCombMapper;
    private final XpdUserExtMapper userExtMapper;
    private final SptalentsdFacade sptalentsdFacade;
    private final XpdUserViewService userViewService;
    private final AppProperties appProperties;
    private final XpdActivityParticipationMemberMapper activityParticipationMemberMapper;
    private final XpdDimMapper xpdDimMapper;
    private final UserDimResultFillContext userDimResultFillContext;


    public XpdUserInfoVO getUserDetail(String orgId, String id) {
        UdpLiteUserPO udpLiteUser = udpLiteUserMapper.selectByUserId(orgId, id);
        XpdUserInfoVO userInfo = new XpdUserInfoVO();
        if (udpLiteUser == null) {
            return userInfo;
        }
        userInfo.setId(udpLiteUser.getId());
        userInfo.setUsername(udpLiteUser.getUsername());
        userInfo.setFullname(udpLiteUser.getFullname());
        userInfo.setPositionName(udpLiteUser.getPositionName());
        userInfo.setDeptName(udpLiteUser.getDeptName());
        return userInfo;
    }

    /**
     * 获取概览人员进度 数据
     *
     * @param xpdId
     * @param userId
     * @return
     */
    public XpdUserPrjResultVO getViewData(String xpdId, String userId){
        XpdPO xpd = xpdMapper.selectByPrimaryKey(xpdId);
        XpdUserPrjResultVO res = new XpdUserPrjResultVO();
        String orgId = xpd.getOrgId();

        // 项目设置
        XpdRulePO rule = ruleMapper.getByXpdId(xpd.getOrgId(), xpdId);
        XpdResultUserPO resultUser = resultUserMapper.findByUserId(orgId, xpdId, userId);
        if (resultUser != null) {
            String xpdLevelId = resultUser.getXpdLevelId();
            XpdLevelPO xpdLevel = xpdLevelMapper.selectByPrimaryKey(xpdLevelId);

            res.setResultLevel(xpdLevel.getLevelName());
            res.setResultType(rule.getResultType());

            Integer resultType = rule.getResultType();

            if (resultType == 1) {
                res.setScoreValue(resultUser.getScoreValue().setScale(2, RoundingMode.HALF_UP));
            } else if (resultType == 2) {
                res.setQualifiedPtg(resultUser.getQualifiedPtg().setScale(0, RoundingMode.HALF_UP));
            } else if (resultType  == 0) {
                dealDimResult(xpdId, userId, orgId, xpd, res);
            }
        }


        // 进度
        // 活动进度
        ActivityMemberStatistics
            activityMemberStatistics = memberStatisticsMapper.selectByActvIdAndUserId(orgId, xpd.getAomPrjId(), userId);
        if (activityMemberStatistics != null) {
            if (activityMemberStatistics.getAllTaskCompletedRate() == null) {
                res.setProgress(BigDecimal.ZERO);
            } else {
                res.setProgress(activityMemberStatistics.getAllTaskCompletedRate().multiply(BigDecimal.valueOf(100)));
            }
        } else {
            res.setProgress(BigDecimal.ZERO);
        }
        // 发展建议
        XpdUserExtPO xpdUserExt = userExtMapper.selectByXpdIdAndUserId(orgId, xpdId, userId);
        if (xpdUserExt != null) {
            res.setSuggestion(xpdUserExt.getSuggestion());
        }
        return res;

    }

    private void dealDimResult(String xpdId, String userId, String orgId, XpdPO xpd, XpdUserPrjResultVO res) {
        List<XpdUserDimGridLevelVO> userDimResultList = getUserDimGridLevelList(xpdId, userId, orgId, xpd);
        List<XpdUserDimGridLevelVO> list = userDimResultList.stream().filter(x -> StringUtils.isNotBlank(x.getDimName())).toList();
        res.setUserDimResultList(list);
    }

    private List<XpdUserDimGridLevelVO> getUserDimGridLevelList(String xpdId, String userId, String orgId, XpdPO xpd) {
        List<XpdUserDimGridLevelVO> userDimResultList = new ArrayList<>();
        List<XpdResultUserDimPO> resultUserDimList = resultUserDimMapper.findByXpdIdAndUserId(orgId, xpdId, userId);
        Map<String, XpdResultUserDimPO> gridDimLevelMap =
                StreamUtil.list2map(resultUserDimList, XpdResultUserDimPO::getSdDimId);
        XpdGridPO xpdGrid = gridMapper.selectByXpdId(orgId, xpdId);
        if (xpdGrid == null) {
            return new ArrayList<>();
        }
        Integer gridType = xpdGrid.getGridType();
        // 维度
        List<XpdDimPO> dimList = dimMapper.listByXpdId(orgId, xpdId);
        List<String> dimIds = dimList.stream().map(XpdDimPO::getSdDimId).toList();
        List<ModelBase4Facade> dimInfoList = spsdAclService.getDimInfoList(orgId, xpd.getModelId(), dimIds);
        log.info("getUserDimGridLevelList modelId={},dimIds={}, dimInfoList={}",xpd.getModelId(), dimIds,  dimInfoList);
        Map<String, ModelBase4Facade> dimMap =
            StreamUtil.list2map(dimInfoList, ModelBase4Facade::getDmId);
        List<XpdGridLevelPO> gridLevelList = gridLevelMapper.listByXpdId(orgId, xpdId);
        Map<String, XpdGridLevelPO> gridLevelMap = StreamUtil.list2map(gridLevelList, XpdGridLevelPO::getId);

        for (XpdDimPO xpdDimPO : dimList) {
            XpdUserDimGridLevelVO userDimResult = new XpdUserDimGridLevelVO();
            userDimResultList.add(userDimResult);

            String sdDimId = xpdDimPO.getSdDimId();

            ModelBase4Facade modelBase4Facade = dimMap.get(xpdDimPO.getSdDimId());
            if (modelBase4Facade == null) {
                continue;
            }
            String dmName = modelBase4Facade.getDmName();
            //Integer dmType = modelBase4Facade.getDmType();
            userDimResult.setDimId(sdDimId);
            userDimResult.setDimName(dmName);
            userDimResult.setDimType(xpdDimPO.getDimType());

            XpdResultUserDimPO xpdResultUserDim = gridDimLevelMap.get(sdDimId);
            if (xpdResultUserDim == null) {
                continue;
            }

            userDimResult.setDimId(xpdResultUserDim.getSdDimId());
            String gridLevelId = xpdResultUserDim.getGridLevelId();
            userDimResult.setGridLevelId(gridLevelId);
            XpdGridLevelPO xpdGridLevel = gridLevelMap.get(gridLevelId);
            if (xpdGridLevel != null) {
                userDimResult.setGridLevelName(xpdGridLevel.getLevelName());
                userDimResult.setOrderIndex(userDimResult.getOrderIndex());
            }
            userDimResult.setScoreValue(xpdResultUserDim.getScoreValue());
            userDimResult.setQualifiedPtg(xpdResultUserDim.getQualifiedPtg());
            userDimResult.setCaliFlag(xpdResultUserDim.getCaliFlag());
            userDimResult.setGridType(gridType);
        }

        return userDimResultList;
    }

    public XpdGridMsgVO getGridData(String xpdId){
        XpdGridMsgVO res = new XpdGridMsgVO();
        XpdPO xpd = xpdMapper.selectByPrimaryKey(xpdId);
        Validate.isNotNull(xpd, "apis.sptalentrv.xpd.prj.not.exist");
        String orgId = xpd.getOrgId();
        XpdGridPO xpdGrid = gridMapper.selectByXpdId(orgId, xpdId);
        Validate.isNotNull(xpdGrid, "apis.sptalentrv.xpd.grid.not.exist");
        res.setGridType(xpdGrid.getGridType());
        res.setConfigType(xpdGrid.getConfigType());


        // 维度列表
        List<XpdDimCombPO> xpdDimCombList = xpdDimCombMapper.listByXpdId(orgId, xpdId);
        List<String> dimIds = new ArrayList<>();
        List<String> xDimIds = xpdDimCombList.stream().map(XpdDimCombPO::getXSdDimId).toList();
        List<String> yDimIds = xpdDimCombList.stream().map(XpdDimCombPO::getYSdDimId).toList();
        dimIds.addAll(xDimIds);
        dimIds.addAll(yDimIds);
        List<DimensionList4Get> baseDimDetails = spsdAclService.getBaseDimDetail(xpdGrid.getOrgId(), dimIds);
        Map<String, String> dimMap =
            StreamUtil.list2map(baseDimDetails, DimensionList4Get::getId, DimensionList4Get::getDmName);

        List<ViewDimCombVO> dimCombList = new ArrayList<>();
        for (XpdDimCombPO xpdDimComb : xpdDimCombList) {
            ViewDimCombVO viewDimCombVO = new ViewDimCombVO();
            viewDimCombVO.setDimCombId(xpdDimComb.getId());
            viewDimCombVO.setDimCombName(xpdDimComb.getCombName());
            viewDimCombVO.setXSdDimId(xpdDimComb.getXSdDimId());
            viewDimCombVO.setXSdDimName(dimMap.get((xpdDimComb.getXSdDimId())));
            viewDimCombVO.setYSdDimId(xpdDimComb.getYSdDimId());
            viewDimCombVO.setYSdDimName(dimMap.get(xpdDimComb.getYSdDimId()));
            dimCombList.add(viewDimCombVO);
        }
        res.setDimCombList(dimCombList);
        // 宫格层级
        List<XpdGridLevelPO> gridLevelList = gridLevelMapper.listByGridIdDesc(orgId, xpdGrid.getId());
        List<XpdGridLevelInfoVO> gridLevelInfoList = new ArrayList<>();
        for (XpdGridLevelPO xpdGridLevel : gridLevelList) {
            XpdGridLevelInfoVO gridLevelInfoVO = new XpdGridLevelInfoVO();
            gridLevelInfoVO.setGridLevelId(xpdGridLevel.getId());
            gridLevelInfoVO.setGridLevelName(xpdGridLevel.getLevelName());
            gridLevelInfoVO.setOrderIndex(xpdGridLevel.getOrderIndex());
            gridLevelInfoVO.setThirdDimColor(xpdGridLevel.decideThirdDimColor(xpdGrid, false,
                appProperties));
            gridLevelInfoList.add(gridLevelInfoVO);
        }
        res.setGridLevelInfoList(gridLevelInfoList);
        // 发展建议
        /*XpdUserExtPO xpdUserExt = userExtMapper.selectByXpdIdAndUserId(orgId, xpdId, userId);
        if (xpdUserExt != null) {
            res.setSuggestion(xpdUserExt.getSuggestion());
        }*/
        return res;
    }

    public XpdUserCellVO getUserGridCell(String orgId,  ViewCell4UserCmd bean) {
        XpdUserCellVO res = new XpdUserCellVO();
        XpdPO xpdPO = xpdMapper.selectById(bean.getXpdId());
        XpdGridPO xpdGrid = gridMapper.selectByXpdId(orgId, xpdPO.getId());
        String dimCombId = bean.getDimCombId();
        if (xpdGrid.getConfigType() == 0) {
            dimCombId = StringUtils.EMPTY;
        }
        List<XpdGridCellPO> xpdGridCellList = gridCellMapper.listByGridIdAndDimCombId(orgId, xpdGrid.getId(), dimCombId);
        if (CollectionUtils.isEmpty(xpdGridCellList)) {
            return res;
        }
        // 宫格显示
        List<XpdGridCellInfoVO> gridCellInfoList = getXpdGridCellInfoList(xpdGridCellList);
        res.setGridCellInfoList(gridCellInfoList);

        XpdDimCombPO dimComb = xpdDimCombMapper.selectByPrimaryKey(bean.getDimCombId());
        if (dimComb == null) {
            return res;
        }
        UserCellIndexDTO userCellIndex =
            resultUserDimMapper.selectIndex4User(orgId, bean, dimComb.getXSdDimId(), dimComb.getYSdDimId());
        if (userCellIndex == null) {
            return res;
        }

        if (xpdGrid.getConfigType() == 0) {
            dimCombId = StringUtils.EMPTY;
        }
        XpdGridCellPO xpdGridCellPO =
            gridCellMapper.selectByDimCombIdAndIndex(orgId, bean.getXpdId(), dimCombId, userCellIndex.getXIndex(),
                userCellIndex.getYIndex());
        res.setCellIndex(xpdGridCellPO.getCellIndex());
        res.setYIndex(xpdGridCellPO.getYIndex());
        res.setXIndex(xpdGridCellPO.getXIndex());
        String userId = bean.getUserId();
        UdpLiteUserPO udpLiteUserPO = udpLiteUserMapper.selectById(userId);

        if (udpLiteUserPO != null) {
            List<XpdUserDimResultsVO> userList = new ArrayList<>();
            XpdUserDimResultsVO user = new XpdUserDimResultsVO();
            UserBaseInfo.Assembler.INSTANCE.update(udpLiteUserPO, user);
            userList.add(user);

            // 使用策略模式填充用户维度结果数据
            List<String> userIds = List.of(userId);
            Map<String, XpdUserDimResultsVO> userInfoMap = userList.stream()
                .collect(Collectors.toMap(XpdUserDimResultsVO::getUserId, u -> u));

            // 从业务库填充用户维度结果
            userDimResultFillContext.fillFromBusiness(orgId, bean.getXpdId(), userIds, userInfoMap);

            res.setUserList(userList);
        }

        return res;
    }


    private List<XpdGridCellInfoVO> getXpdGridCellInfoList(List<XpdGridCellPO> xpdGridCellList) {
        List<XpdGridCellInfoVO> gridCellInfoList = new ArrayList<>();
        log.info("getXpdGridCellInfoList GridColorMap={}", appProperties.getGridColorMap());
        for (XpdGridCellPO gridCell : xpdGridCellList) {
            XpdGridCellInfoVO gridCellInfoVO = new XpdGridCellInfoVO();
            gridCellInfoVO.setId(gridCell.getId());
            gridCellInfoVO.setCellName(gridCell.getCellName());
            gridCellInfoVO.setCellIndex(gridCell.getCellIndex());
            gridCellInfoVO.setCellColor(gridCell.getCellColor());
            if (StringUtils.isNotBlank(gridCell.getCellColor())) {
                gridCellInfoVO.setTextColor(appProperties.getGridColorMap().get(gridCell.getCellColor().replace("#", "")));
            }

            gridCellInfoList.add(gridCellInfoVO);
        }
        return gridCellInfoList;
    }

    public List<XpdUserDimGridLevelVO> findUserGridLevel(String xpdId, String userId) {
        XpdPO xpd = xpdMapper.selectByPrimaryKey(xpdId);
        Validate.isNotNull(xpd, "apis.sptalentrv.xpd.prj.not.exist");

        String orgId = xpd.getOrgId();
        return getUserDimGridLevelList(xpdId, userId, orgId, xpd);
    }

    public List<XpdDimResult4ViewVO> findDimResult(String xpdId, String userId) {
        XpdPO xpd = xpdMapper.selectByPrimaryKey(xpdId);
        Validate.isNotNull(xpd, "apis.sptalentrv.xpd.prj.not.exist");
        String orgId = xpd.getOrgId();
        List<XpdUserDimGridLevelVO> userDimGridLevelList = getUserDimGridLevelList(xpdId, userId, orgId, xpd);
        List<XpdDimRulePO> xpdDimRuleList = dimRuleMapper.listByXpdId(orgId, xpdId);
        Map<String, XpdDimRulePO> xpdDimRuleMap = StreamUtil.list2map(xpdDimRuleList, XpdDimRulePO::getSdDimId);

        List<XpdUserLevelNumDto> levelNumList = resultUserDimMapper.findLevelNum(orgId, xpdId);
        List<XpdGridLevelPO> gridLevelList = gridLevelMapper.listByXpdId(orgId, xpdId);
        Map<String, Integer> gridLevelMap =
            StreamUtil.list2map(gridLevelList, XpdGridLevelPO::getId, XpdGridLevelPO::getOrderIndex);
        for (XpdUserLevelNumDto userLevelNum : levelNumList) {
            Integer orderIndex = gridLevelMap.get(userLevelNum.getGridLevelId());
            if (orderIndex != null) {
                userLevelNum.setOrderIndex(orderIndex);
            }
        }

        Map<String, List<XpdUserLevelNumDto>> userLevelNumMap =
            levelNumList.stream().collect(Collectors.groupingBy(XpdUserLevelNumDto::getDimId));

        // 计算所处位置
        List<ViewUserDimDTO> userDimDtos = resultUserDimMapper.findUser(orgId, xpdId);


        List<XpdDimResult4ViewVO> resList = new ArrayList<>();
        for (XpdUserDimGridLevelVO userDimGridLevel : userDimGridLevelList) {
            String dimName = userDimGridLevel.getDimName();
            String dimId = userDimGridLevel.getDimId();
            XpdDimResult4ViewVO dimResult4ViewVO = new XpdDimResult4ViewVO();
            dimResult4ViewVO.setDimId(userDimGridLevel.getDimId());
            dimResult4ViewVO.setDimName(dimName);
            dimResult4ViewVO.setGridLevelName(userDimGridLevel.getGridLevelName());
            dimResult4ViewVO.setCaliFlag(userDimGridLevel.getCaliFlag());

            XpdDimRulePO xpdDimRule = xpdDimRuleMap.get(dimId);
            if (xpdDimRule == null) {
                continue;
            }
            dealPosPercent(userDimGridLevel, userLevelNumMap, dimId, dimResult4ViewVO, userId, xpdId, xpdDimRule,
                userDimDtos);
            if (userDimGridLevel.getDimType() == 5) {

                dimResult4ViewVO.setResult(findDim4Perf(orgId, xpdId, userDimGridLevel.getDimId(), userId));
                resList.add(dimResult4ViewVO);
                continue;
            }


            if (xpdDimRule.getResultType() == 0) {
                if (userDimGridLevel.getScoreValue() != null) {
                    dimResult4ViewVO.setResult(userDimGridLevel.getScoreValue().setScale(2, RoundingMode.HALF_UP).toString());
                }
            } else {
                if (userDimGridLevel.getQualifiedPtg() != null) {
                    dimResult4ViewVO.setResult(userDimGridLevel.getQualifiedPtg().setScale(0, RoundingMode.HALF_UP).toString() + "%");
                }

            }

            resList.add(dimResult4ViewVO);
        }
        return resList;
    }

    private void dealPosPercent(
        XpdUserDimGridLevelVO userDimGridLevel,
        Map<String, List<XpdUserLevelNumDto>> userLevelNumMap,
        String dimId,
        XpdDimResult4ViewVO dimResult4ViewVO, String userId, String xpdId,
        XpdDimRulePO xpdDimRule, List<ViewUserDimDTO> userDimDtos) {
        Integer dimType = userDimGridLevel.getDimType();
        // 绩效维度处理
        if (dimType == 5) {
            List<XpdUserLevelNumDto> xpdUserLevelNumDtos = userLevelNumMap.get(dimId);
            if (CollectionUtils.isEmpty(xpdUserLevelNumDtos)) {
                return;
            }
            // 总人数
            int allUserNum = xpdUserLevelNumDtos.stream().mapToInt(XpdUserLevelNumDto::getUserNum).sum();
            // 比我维度分层低的人数
            Optional<XpdUserLevelNumDto> first = xpdUserLevelNumDtos.stream()
                .filter(x -> x.getGridLevelId().equals(userDimGridLevel.getGridLevelId()))
                .findFirst();
            if (first.isEmpty()) {
                log.error("dealPosPercent error xpdId={}, dimId={}, userId={}", xpdId, dimId, userId);
                return;
            }
            XpdUserLevelNumDto userLevelNumDto = first.get();
            int orderIndex = userLevelNumDto.getOrderIndex();
            int lowLevelNum = xpdUserLevelNumDtos.stream()
                .filter(x -> x.getOrderIndex() < orderIndex)
                .mapToInt(XpdUserLevelNumDto::getUserNum)
                .sum();
            dimResult4ViewVO.setPosPercent(MathUtil.divide4Int(lowLevelNum, allUserNum, 2, RoundingMode.HALF_UP));
        } else {
            List<ViewUserDimDTO> list = userDimDtos.stream().filter(x -> x.getDimId().equals(dimId)).toList();
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            Integer resultType = xpdDimRule.getResultType();
            if (resultType == 0) {
                Optional<ViewUserDimDTO> first = list.stream().filter(x -> x.getUserId().equals(userId)).findFirst();
                if (first.isPresent()) {
                    BigDecimal scoreValue = first.get().getScoreValue();
                    long count = list.stream().filter(x -> scoreValue.compareTo(x.getScoreValue()) > 0).count();
                    dimResult4ViewVO.setPosPercent(MathUtil.divide4Int(count, list.size(), 2, RoundingMode.HALF_UP));
                }
            } else {
                Optional<ViewUserDimDTO> first = list.stream().filter(x -> x.getUserId().equals(userId)).findFirst();
                if (first.isPresent()) {
                    BigDecimal qualifiedPtg = first.get().getQualifiedPtg();
                    long count = list.stream().filter(x -> qualifiedPtg.compareTo(x.getQualifiedPtg()) > 0).count();
                    dimResult4ViewVO.setPosPercent(MathUtil.divide4Int(count, list.size(), 2, RoundingMode.HALF_UP));
                }
            }
        }

    }


    private String findDim4Perf(String orgId, String xpdId, String dimId, String userId) {
        XpdResultUserDimPO resultUserDim = resultUserDimMapper.findByXpdIdAndUserIdAndDimId(orgId, xpdId, userId, dimId);
        if (resultUserDim == null) {
            return StringUtils.EMPTY;
        }
        XpdResultUserIndicatorPO xpdResultUserIndicator = resultUserIndicatorMapper.selectByXpdIdAndResultDimIdAndUserId(orgId, xpdId, resultUserDim.getId(), userId);
        if (xpdResultUserIndicator == null) {
            return StringUtils.EMPTY;
        }
        return xpdResultUserIndicator.getPerfSummary();
    }

    public List<XpdDimIndicator4ViewVO> findDimIndicator(String xpdId, String userId) {
        XpdPO xpd = xpdMapper.selectByPrimaryKey(xpdId);
        Validate.isNotNull(xpd, "apis.sptalentrv.xpd.prj.not.exist");
        String orgId = xpd.getOrgId();
        //List<XpdUserDimGridLevelVO> userDimGridLevelList = getUserDimGridLevelList(xpdId, userId, orgId, xpd);
        List<XpdDimPO> dimPOS = xpdDimMapper.listByXpdId(orgId, xpdId);
        if (CollectionUtils.isEmpty(dimPOS)) {
            return new ArrayList<>();
        }
        /*if (CollectionUtils.isEmpty(userDimGridLevelList)) {
            return new ArrayList<>();
        }*/
        List<String> dimIds = dimPOS.stream().map(XpdDimPO::getSdDimId).toList();
        if (CollectionUtils.isEmpty(dimIds)) {
            return new ArrayList<>();
        }
        //List<String> dimIds = userDimGridLevelList.stream().map(XpdUserDimGridLevelVO::getDimId).toList();
        /*IndicatorDimReq req = new IndicatorDimReq();
        req.setOrgId(orgId);
        req.setModelId(xpd.getModelId());
        req.setDimIds(dimIds);
        List<IndicatorDto> indicatorList = spsdAclService.getIndicatorDetailByDims(req);*/
        List<IndicatorDto> indicatorList =
            spsdAclService.getLastIndicatorByDims(orgId, xpd.getModelId(), dimIds);

        // 检查指标是否加入到了维度规则中
        List<XpdDimRuleCalcPO> xpdDimRuleCalcList = dimRuleCalcMapper.listByXpdId(orgId, xpdId);
        Map<String, List<String>> ruleCalcMap = xpdDimRuleCalcList.stream()
            .collect(Collectors.groupingBy(
                XpdDimRuleCalcPO::getSdDimId,
                Collectors.mapping(XpdDimRuleCalcPO::getSdIndicatorId, Collectors.toList())
            ));
        ModelInfo modelInfo = sptalentsdFacade.getModelInfo(orgId, xpd.getModelId());

        Model4UserView
            model4UserView = JSON.parseObject(JSON.toJSONString(modelInfo), Model4UserView.class);
        List<XpdDimIndicator4ViewVO> dms = model4UserView.getDms();

        // 是否达标
        List<String> allIndicatorIds = indicatorList.stream().map(IndicatorDto::getItemId).toList();
        log.info("findDimIndicator no allIndicatorIds indicatorList{}", indicatorList);
        if (CollectionUtils.isEmpty(allIndicatorIds)) {
            log.info("findDimIndicator allIndicatorIds empty xpdId={}, userId = {}", xpd.getId(), userId);
            log.info("findDimIndicator allIndicatorIds indicatorList{}", indicatorList);
            return Lists.newArrayList();
        }
        List<XpdResultUserIndicatorPO> resultUserIndicatorList =
            resultUserIndicatorMapper.findByIndicatorIdsAndUserId(orgId, xpdId, allIndicatorIds, userId);
        Map<String, XpdResultUserIndicatorPO> userScoreMap =
            StreamUtil.list2map(resultUserIndicatorList, XpdResultUserIndicatorPO::getSdIndicatorId);

        // 获取所有活动名称
        Map<String, String> resultDetailMap =
            StreamUtil.list2map(resultUserIndicatorList, XpdResultUserIndicatorPO::getSdIndicatorId,
                XpdResultUserIndicatorPO::getResultDetail);
        List<ActivityInfo4UserDto> userInfoList = new ArrayList<>();
        resultDetailMap.forEach((k,v) ->{
            List<DimUserIndicatorResultDto> dimUserIndicatorResultDtos =
                BeanHelper.json2Bean(v, List.class, DimUserIndicatorResultDto.class);

            for (DimUserIndicatorResultDto dimUserIndicatorResultDto : dimUserIndicatorResultDtos) {
                ActivityInfo4UserDto dto = new ActivityInfo4UserDto();
                BeanCopierUtil.copy(dimUserIndicatorResultDto, dto);
                dto.setIndicatorId(k);
                userInfoList.add(dto);
            }
        });
        List<String> refIds = userInfoList.stream().map(ActivityInfo4UserDto::getRefId).toList();
        List<ActivityInfo4UserDto> activityInfo4UserDtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(refIds)) {
            activityInfo4UserDtos = rvActivityArrangeItemMapper.listInfoByIds(orgId, refIds);
        }

        // 导入活动
        List<XpdImportPO> importPOS = new ArrayList<>();
        if (!CollectionUtils.isEmpty(refIds)) {
            importPOS = importMapper.selectByIds(orgId, refIds);
        }

        List<String> importDimIds = importPOS.stream().map(XpdImportPO::getSdDimId).toList();
        List<ModelBase4Facade> dimInfoList = spsdAclService.getDimInfoList(orgId, xpd.getModelId(), importDimIds);
        if (CollectionUtils.isEmpty(dimInfoList)) {
            dimInfoList = new ArrayList<>();
        }
        Map<String, String> dimMap =
            StreamUtil.list2map(dimInfoList, ModelBase4Facade::getDmId, ModelBase4Facade::getDmName);
        Map<String, String> importNameMap = new HashMap<>();
        for (XpdImportPO importPO : importPOS) {
            String dmName = dimMap.get(importPO.getSdDimId());
            if (StringUtils.isBlank(dmName)) {
                continue;
            }
            importNameMap.put(importPO.getId(), dmName);
        }


        Map<String, String> refActvNameMap = StreamUtil.list2map(activityInfo4UserDtos, ActivityInfo4UserDto::getRefId,
            ActivityInfo4UserDto::getActvName);

        // 指标总分   标准分
        List<XpdResultIndicatorPO> resultIndicatorList =
            resultIndicatorMapper.findByIndicatorIds(orgId, xpdId, allIndicatorIds);

        Map<String, XpdResultIndicatorPO> resultIndicatorMap =
            StreamUtil.list2map(resultIndicatorList, XpdResultIndicatorPO::getSdIndicatorId);

        for (ActivityInfo4UserDto activityInfo4UserDto : userInfoList) {
            String refId = activityInfo4UserDto.getRefId();
            int refType = activityInfo4UserDto.getRefType();
            if (refType == DimRuleCalcRefEnum.AOM_ACT.getCode()) {
                String actvName = refActvNameMap.get(refId);
                if (StringUtils.isBlank(actvName)) {
                    continue;
                }
                activityInfo4UserDto.setActvName(actvName);
            } else if (refType == DimRuleCalcRefEnum.IMPORT_DATA.getCode()) {
                String actvName = importNameMap.get(refId);
                if (StringUtils.isBlank(actvName)) {
                    continue;
                }
                activityInfo4UserDto.setActvName(actvName);
            }
        }
        log.info("mark01 userInfoList ={}", userInfoList);
        Map<String, List<ActivityInfo4UserDto>> actvMap =
            userInfoList.stream().collect(Collectors.groupingBy(ActivityInfo4UserDto::getIndicatorId));
        List<XpdImportPO> importActList = importMapper.selectByXpdIdAndOrgId(orgId, xpdId);

        Map<String, Integer> importTypeMap =
            StreamUtil.list2map(importActList, XpdImportPO::getSdDimId, XpdImportPO::getImportType);
        // 检查
        List<XpdDimIndicator4ViewVO> resList = new ArrayList<>();
        for (XpdDimPO dimPO : dimPOS) {
            // 绩效单独处理
            if (dimPO.getDimType() == 5) {
                continue;
            }
            int importType = userViewService.chkImportType(dimPO.getSdDimId(), importTypeMap);

            List<String> dimNames = userViewService.findAndCollectDmNamesByDmId(dms, dimPO.getSdDimId(), dms, resList, importType, dimPO.getSdDimId());
        }

        for (XpdDimIndicator4ViewVO res : resList) {
            Integer importType = res.getImportType();
            // 是维度的清空数据
            if (importType == 1) {
                res.setDetails(new ArrayList<>());
            } else {
                userViewService.dealDimIndicator(res, ruleCalcMap, actvMap, resultIndicatorMap, userScoreMap);
            }

        }


        return resList;

    }

    @Nullable
    public XpdChart4UserAllVO findPerf(String orgId, String xpdId, String userId) {
        XpdChart4UserAllVO res = new XpdChart4UserAllVO();
        // 检查盘点规则中是否有绩效维度设置
        List<XpdDimPO> dimList = dimMapper.listByXpdId(orgId, xpdId);
        List<XpdDimPO> perfDimList = dimList.stream().filter(x -> x.getDimType() == 5).toList();
        if (CollectionUtils.isEmpty(perfDimList)) {
            return null;
        }

        List<XpdBarChart4UserVO> result = new ArrayList<>();
        List<ActivityArrangeItem> perfActvList =
            xpdService.getPerfActvsByXpdId(orgId, xpdId);
        if (CollectionUtils.isEmpty(perfActvList)) {
            // 盘点项目中没有绩效活动
            return null;
        }
        // 获取计算方式
        List<XpdDimRulePO> xpdDimRulePOS = dimRuleMapper.listByXpdId(orgId, xpdId);
        List<XpdDimRulePO> list =
            xpdDimRulePOS.stream().filter(x -> (x.getCalcType() == 2 || x.getCalcType() == 3)).toList();
        if (CollectionUtils.isNotEmpty(list)) {
            res.setCalcType(list.get(0).getCalcType());
            //return null;
        }

        // 处理绩效等级
        Map<Integer, Integer> gradeIndexMap = new HashMap<>();
        Collection<PerfGradePO> perfGrades = perfGradeMapper.selectByOrgId(orgId);
        int index = 1;
        int size = perfGrades.size();
        List<PerfGradeVO> perfGradeList = new ArrayList<>();
        for (PerfGradePO perfGradePO : perfGrades) {
            int tempIndex = index++;
            int tempSize = size--;
            gradeIndexMap.put(perfGradePO.getGradeValue(), tempIndex);
            PerfGradeVO perfGradeVO = new PerfGradeVO();
            perfGradeVO.setGradeName(perfGradePO.getGradeName());
            perfGradeVO.setGradeValue(tempIndex);
            perfGradeVO.setOrderIndex(tempSize);
            perfGradeList.add(perfGradeVO);
        }
        res.setPerfGradeList(perfGradeList);

        List<PerfPO> perfs = getPerfPOS(orgId, userId, perfActvList);
        if (CollectionUtils.isNotEmpty(perfs)) {
            Map<Integer, String> gradesMap = new HashMap<>(8);
            if (CollectionUtils.isNotEmpty(perfGrades)) {

                gradesMap = StreamUtil.list2map(perfGrades, PerfGradePO::getGradeValue, PerfGradePO::getGradeName);
            }
            // 数据过滤
            if (CollectionUtils.isNotEmpty(perfs)) {
                Map<String, PerfPO> perfMap = StreamUtil.list2map(perfs, PerfPO::getPeriodId);
                List<String> periodIds = perfs.stream().map(PerfPO::getPeriodId).collect(Collectors.toList());
                List<PerfPeriodPO> perfPeriods = perfPeriodMapper.selectByOrgIdAndIds(orgId, periodIds);
                // 判断项目中是都有绩效维度

                if (CollectionUtils.isNotEmpty(perfPeriods)) {

                    Map<Integer, String> finalGradesMap = gradesMap;
                    perfPeriods.forEach(p -> {
                        XpdBarChart4UserVO perfInfoVO = new XpdBarChart4UserVO();
                        perfInfoVO.setPeriodName(p.getPeriodName());
                        perfInfoVO.setSort(perfInfoVO.getSort());
                        perfInfoVO.setLevel(
                            null != perfMap.get(p.getId()) ? perfMap.get(p.getId()).getPeriodLevel() : 0);
                        perfInfoVO.setLevelStr(null != perfMap.get(p.getId()) ?
                            finalGradesMap.get(perfMap.get(p.getId()).getPeriodLevel()) :
                            "-1");


                        perfInfoVO.setPerfPoint(null != perfMap.get(p.getId()) ?
                            perfMap.get(p.getId()).getPerfPoint() : null);

                        perfInfoVO.setPerfScore(null != perfMap.get(p.getId()) ?
                            perfMap.get(p.getId()).getPerfScore() : null);
                        result.add(perfInfoVO);
                    });
                }
            }
        }
        // 把绩效等级换成正常顺序 1,2,3,4.。。
        result.forEach( x-> {
            Integer level = gradeIndexMap.get(x.getLevel());
            x.setLevel(level == null ? 0 : level);
        });
        res.setMaxLevel(gradeIndexMap.size());
        res.setDetails(result);
        return res;
    }

    private List<PerfPO> getPerfPOS(String orgId, String userId, List<ActivityArrangeItem> perfActvList) {
        List<String> refIds = perfActvList.stream().map(ActivityArrangeItem::getRefId).toList();
        List<ActivityPerfPO> activityPerfList = activityPerfMapper.selectByIds(orgId, refIds);
        List<String> periodIdsStrs = activityPerfList.stream().map(ActivityPerfPO::getPeriodIds).toList();
        List<String> allPeriodIds = new ArrayList<>();
        for (String periodIdsStr : periodIdsStrs) {
            String[] periodIds = periodIdsStr.split(";");
            allPeriodIds.addAll(List.of(periodIds));
        }


        List<PerfPO> perfs = perfMapper.selectByUserIdsAndPeriodIds(orgId, Lists.newArrayList(userId), allPeriodIds);
        return perfs;
    }

    public RvUsers4Get userInfoDetail(String orgId, String userId, SearchDTO bean) {
        QueryUtil.Search search = QueryUtil.parse(bean);
        String xpdId = search.getEqVal("xpdId");
        RvUsers4Get res = new RvUsers4Get();
        UdpLiteUserPO udpLiteUserPO = udpLiteUserMapper.selectById(userId);
        res.setUserids(ApassEntityUtils.createDrawer4UserDTO(udpLiteUserPO.getFullname(), userId,
            udpLiteUserPO.getUsername(), udpLiteUserPO.getDeptName(), String.valueOf(udpLiteUserPO.getStatus()),
            udpLiteUserPO.getPositionName(), udpLiteUserPO.getImgUrl()));


        XpdResultUserPO resultUser = resultUserMapper.findByUserId(orgId, xpdId, userId);
        if (resultUser == null) {
            return res;
        }
        String xpdLevelId = resultUser.getXpdLevelId();
        XpdLevelPO xpdLevel = xpdLevelMapper.selectByPrimaryKey(xpdLevelId);
        if (xpdLevel != null) {
            res.setXpdlevelid(ApassEntityUtils.createAmSlDrawerIdName(xpdLevelId, xpdLevel.getLevelName()));
        }
        return res;
    }

    public List<XpdUserPrjVO> findUserXpdPrj(String orgId, String userId){
        return activityParticipationMemberMapper.findPrjListByUserId(orgId, userId);
    }


}
