package com.yxt.talent.rv.application.xpd.report.enums;

/**
 * <AUTHOR>
 * @since 2024/12/13
 */
public enum EducationLevelEnum {


    DOCTORATE("博士"), MASTER("硕士"), BACHELOR("本科"), ASSOCIATE("大专"), VOCATIONAL_HIGH_SCHOOL(
            "职高"), TECHNICAL_SECONDARY_SCHOOL("中专/中技"), HIGH_SCHOOL("高中"), JUNIOR_HIGH_SCHOOL(
            "初中"), PRIMARY_SCHOOL("小学"), OTHER("其他");

    private final String description;

    EducationLevelEnum(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

    public static EducationLevelEnum fromString(String description) {
        for (EducationLevelEnum level : EducationLevelEnum.values()) {
            if (level.getDescription().equals(description)) {
                return level;
            }
        }
        return OTHER;
    }

    @Override
    public String toString() {
        return description;
    }


}
