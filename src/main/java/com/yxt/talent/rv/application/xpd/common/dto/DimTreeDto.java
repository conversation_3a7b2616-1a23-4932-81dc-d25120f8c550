package com.yxt.talent.rv.application.xpd.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/10 18:13
 */
@Data
@Builder
@Schema(description = "维度树结构")
public class DimTreeDto {

    @Schema(description = "人才标准的维度ID")
    private String sdDimId;

    @Schema(description = "维度名称")
    private String dimName;

    @Schema(description = "维度类型[标准：指标来源] 0-普通/1-能力/2-技能/3-知识/4-任务")
    private Integer dimType;

    @Schema(description = "级别类型，0-父级，1-末级")
    private Integer levelType;

    @Schema(description = "维度要求 1：门槛项/2：匹配项/3：加分项/4：否决项/0：无 仅选盘点维度时用")
    private Integer deqType;

    @Schema(description = "排序")
    private Integer orderIndex;

    @Schema(description = "子维度列表")
    List<DimTreeDto> subDims;
}
