package com.yxt.talent.rv.application.xpd.common.enums;

/**
 * <AUTHOR>
 * @date 2025/2/14
 */
public enum ViewImportTypeEnum {
    NO_IMPORT(0,"非导入活动"),
    IMPORT_DIM(1, "维度导入"),
    IMPORT_INDICATOR(2, "指标导入");

    private Integer importType;

    private String desc;

    ViewImportTypeEnum(Integer importType, String desc){
        this.importType = importType;
        this.desc = desc;
    }

    public Integer getImportType() {
        return importType;
    }

    public String getDesc() {
        return desc;
    }
}
