package com.yxt.talent.rv.application.perf.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(name = "绩效等级bean对象")
public class PerfGradeDTO {

    @Schema(description = "id")
    private String id;

    @Schema(description = "绩效等级名称")
    private String gradeName;

    @Schema(description = "绩效等级description")
    private Integer gradeValue;

    @Schema(description = "绩效等级排序：等级高的orderIndex值小")
    private Integer orderIndex = 0;

    @Schema(description = "是否被使用，0否，1是")
    private Integer used = 0;

    @Schema(description = "状态，0禁用，1启用")
    private Integer state = 1;
}
