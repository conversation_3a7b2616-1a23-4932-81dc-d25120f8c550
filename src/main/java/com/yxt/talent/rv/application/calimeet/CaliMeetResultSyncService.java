package com.yxt.talent.rv.application.calimeet.service;

import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.talent.rv.application.xpd.common.dto.*;
import com.yxt.talent.rv.application.xpd.result.calculator.UserDimCombResultCalculator;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.calimeet.CalimeetRecordMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.*;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.*;
import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class CaliMeetResultSyncService {

    private final CalimeetMapper calimeetMapper;
    private final CalimeetRecordMapper calimeetRecordMapper;
    private final XpdUserExtMapper xpdUserExtMapper;
    private final XpdResultUserDimMapper xpdResultUserDimMapper;
    private final XpdResultUserIndicatorMapper xpdResultUserIndicatorMapper;
    private final XpdResultUserMapper xpdResultUserMapper;
    private final XpdResultUserDimcombMapper xpdResultUserDimcombMapper;
    private final XpdDimCombMapper xpdDimCombMapper;
    private final XpdLevelMapper xpdLevelMapper;
    private final UserDimCombResultCalculator userDimCombResultCalculator;

    /**
     * 将校准结果同步到盘点项目中的员工结果
     *
     * @param calimeet   CalimeetPO
     * @param operatorId 操作人ID
     * @param orgId      组织ID
     */
    public void syncCalimeetData(CalimeetPO calimeet, String operatorId, String orgId) {
        String xpdId = calimeet.getXpdId();
        String calimeetId = calimeet.getId();
        log.info(
            "LOG21493:开始同步校准会数据到盘点项目，calimeetId={}, xpdId={}, orgId={}", calimeet.getId(), xpdId, orgId);

        // 2. 查询校准会的所有被校准人的结果
        List<CalimeetDimResultDto> calimeetRecords = calimeetRecordMapper.getAllRecordsByCalimeetId(orgId, calimeetId);
        if (CollectionUtils.isEmpty(calimeetRecords)) {
            log.info("校准会没有校准记录，跳过同步，calimeetId={}", calimeetId);
            return;
        }

        log.info("LOG21503:找到{}条校准记录需要同步", calimeetRecords.size());

        // 3. 同步suggestion到XpdUserExtPO
        syncSuggestionToXpdUserExt(orgId, xpdId, calimeetRecords, operatorId);

        // 4. 同步到xpd结果表：XpdResultUserPO, XpdResultUserDimPO, XpdResultUserDimcombPO, XpdResultUserIndicatorPO
        syncCalimeetResultsToXpd(orgId, xpdId, calimeetRecords, operatorId, calimeetId);

        log.info("LOG21513:校准会数据同步完成，calimeetId={}", calimeetId);
    }

    /**
     * 同步发展建议到XpdUserExtPO
     */
    private void syncSuggestionToXpdUserExt(
        String orgId, String xpdId, List<CalimeetDimResultDto> calimeetRecords, String operatorId) {
        log.info("LOG21483:开始同步发展建议到XpdUserExt表，记录数量={}", calimeetRecords.size());

        List<String> userIds = calimeetRecords.stream()
            .map(CalimeetDimResultDto::getUserId)
            .filter(StringUtils::isNotBlank)
            .distinct()
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(userIds)) {
            log.info("没有有效的用户ID，跳过同步发展建议");
            return;
        }

        // 查询现有的XpdUserExt记录
        List<XpdUserExtPO> existingRecords = xpdUserExtMapper.selectByXpdIdAndUserIds(orgId, xpdId, userIds);
        Map<String, XpdUserExtPO> existingMap = existingRecords.stream()
            .collect(Collectors.toMap(XpdUserExtPO::getUserId, Function.identity()));

        // 构建校准记录映射
        Map<String, String> suggestionMap = calimeetRecords.stream()
            .filter(record -> StringUtils.isNotBlank(record.getUserId()))
            .collect(Collectors.toMap(
                CalimeetDimResultDto::getUserId,
                record -> StringUtils.defaultString(record.getSuggestion(), ""),
                (existing, replacement) -> replacement
            ));

        List<XpdUserExtPO> toUpdate = new ArrayList<>();
        List<XpdUserExtPO> toInsert = new ArrayList<>();

        for (String userId : userIds) {
            String suggestion = suggestionMap.get(userId);
            XpdUserExtPO existing = existingMap.get(userId);

            if (existing != null) {
                // 更新现有记录
                existing.setSuggestion(suggestion);
                existing.setUpdateUserId(operatorId);
                existing.setUpdateTime(new Date());
                toUpdate.add(existing);
            } else {
                // 创建新记录
                XpdUserExtPO newRecord = new XpdUserExtPO();
                newRecord.setId(ApiUtil.getUuid());
                newRecord.setOrgId(orgId);
                newRecord.setXpdId(xpdId);
                newRecord.setUserId(userId);
                newRecord.setSuggestion(suggestion);
                newRecord.setDeleted(0);
                EntityUtil.setAuditFields(newRecord, operatorId);
                toInsert.add(newRecord);
            }
        }

        // 批量更新
        if (CollectionUtils.isNotEmpty(toUpdate)) {
            xpdUserExtMapper.batchUpdateRvXpdUserExt(toUpdate);
            log.info("LOG21523:更新了{}条XpdUserExt记录的发展建议", toUpdate.size());
        }

        // 批量插入
        if (CollectionUtils.isNotEmpty(toInsert)) {
            xpdUserExtMapper.insertBatch(toInsert);
            log.info("LOG21533:插入了{}条新的XpdUserExt记录", toInsert.size());
        }
    }

    /**
     * 同步校准结果到XPD结果表
     */
    private void syncCalimeetResultsToXpd(
        String orgId, String xpdId, List<CalimeetDimResultDto> calimeetRecords, String operatorId, String calimeetId) {
        log.info("开始同步校准结果到XPD结果表，记录数量={}", calimeetRecords.size());

        // 获取校准会信息以确定校准类型
        if (CollectionUtils.isEmpty(calimeetRecords)) {
            return;
        }

        CalimeetPO calimeet = calimeetMapper.selectByIdAndOrgId(calimeetId, orgId);
        if (calimeet == null) {
            log.warn("无法获取校准会信息，跳过同步");
            return;
        }

        int calimeetType = calimeet.getCalimeetType(); // 校准会类型：0-维度分层结果，1-维度结果，2-指标结果

        for (CalimeetDimResultDto record : calimeetRecords) {
            if (StringUtils.isBlank(record.getUserId())) {
                continue;
            }

            try {
                // 解析校准详情
                CaliUpdateUserResultWrapDto caliDetails = parseCalimeetDetails(record.getCaliDetails());
                if (caliDetails == null || CollectionUtils.isEmpty(caliDetails.getUserResults())) {
                    log.debug("LOG21553:用户{}没有校准详情数据，跳过", record.getUserId());
                    continue;
                }
                CaliDimResultWrapDto resultDetails = parseCalimeetResults(record.getResultDetails());

                // 用户校准之后，重新计算的盘点项目结果， 维度结果，指标结果等数据，如果某个维度结果经过校准之后没有计算出来任务结果，这里不会存储，复制的时候也需要清理掉原有的校准结果
                List<CaliUpdateUserResultDto> userResults = caliDetails.getUserResults();

                // 根据校准会类型同步不同的结果表 (CaliMeetTypeEnum)
                switch (calimeetType) {
                    case 0: // CaliMeetTypeEnum.LEVEL - 维度分层结果
                        syncUserDimLevelResults(
                            orgId, xpdId, record.getUserId(), userResults, resultDetails, operatorId);
                        break;
                    case 1: // CaliMeetTypeEnum.DIM - 维度结果
                        syncUserDimResults(orgId, xpdId, record.getUserId(), userResults, resultDetails, operatorId);
                        break;
                    case 2: // CaliMeetTypeEnum.INDICATOR - 指标结果
                        syncUserIndicatorResults(
                            orgId, xpdId, record.getUserId(), userResults, resultDetails, operatorId);
                        break;
                    default:
                        log.warn("未知的校准会类型：{}", calimeetType);
                }

            } catch (Exception e) {
                log.error("同步用户{}的校准结果时发生异常", record.getUserId(), e);
            }
        }

        log.info("校准结果同步到XPD结果表完成");
    }

    private CaliDimResultWrapDto parseCalimeetResults(String resultDetails) {
        if (StringUtils.isBlank(resultDetails)) {
            return null;
        }

        try {
            return BeanHelper.json2Bean(resultDetails, CaliDimResultWrapDto.class);
        } catch (Exception e) {
            log.warn("LOG21563:解析校准结果JSON失败：{}", resultDetails, e);
            return null;
        }
    }


    /**
     * 解析校准详情JSON
     */
    @Nullable
    private CaliUpdateUserResultWrapDto parseCalimeetDetails(String caliDetails) {
        if (StringUtils.isBlank(caliDetails)) {
            return null;
        }

        try {
            return BeanHelper.json2Bean(caliDetails, CaliUpdateUserResultWrapDto.class);
        } catch (Exception e) {
            log.warn("LOG21543:解析校准详情JSON失败：{}", caliDetails, e);
            return null;
        }
    }

    /**
     * 同步用户维度分层结果
     */
    private void syncUserDimLevelResults(
        String orgId, String xpdId, String userId,
        List<CaliUpdateUserResultDto> userResults, CaliDimResultWrapDto resultDetails, String operatorId) {
        log.debug("LOG21573:同步用户{}的维度分层结果", userId);

        // 1. 同步项目级别结果
        syncUserProjectResult(orgId, xpdId, userId, resultDetails, operatorId);

        // 2. 以resultDetails为准，完整同步维度结果（先清理后同步）
        syncCompleteUserDimResults(orgId, xpdId, userId, resultDetails, operatorId);

        // 3. 同步维度组结果
        syncUserDimCombResults(orgId, xpdId, userId, resultDetails, operatorId);
    }

    /**
     * 同步用户维度结果
     */
    private void syncUserDimResults(
        String orgId, String xpdId, String userId,
        List<CaliUpdateUserResultDto> userResults, CaliDimResultWrapDto resultDetails, String operatorId) {
        log.debug("同步用户{}的维度结果", userId);

        // 1. 同步项目级别结果
        syncUserProjectResult(orgId, xpdId, userId, resultDetails, operatorId);

        // 2. 以resultDetails为准，完整同步维度结果（先清理后同步）
        syncCompleteUserDimResults(orgId, xpdId, userId, resultDetails, operatorId);

        // 3. 同步维度组结果
        syncUserDimCombResults(orgId, xpdId, userId, resultDetails, operatorId);
    }

    /**
     * 同步用户指标结果
     */
    private void syncUserIndicatorResults(
        String orgId, String xpdId, String userId,
        List<CaliUpdateUserResultDto> userResults, CaliDimResultWrapDto resultDetails,
        String operatorId) {
        log.debug("同步用户{}的指标结果", userId);

        // 1. 同步项目级别结果
        syncUserProjectResult(orgId, xpdId, userId, resultDetails, operatorId);

        // 2. 以resultDetails为准，完整同步指标结果（先清理后同步）
        syncCompleteUserIndicatorResults(orgId, xpdId, userId, resultDetails, operatorId);

        // 3. 同步维度组结果
        syncUserDimCombResults(orgId, xpdId, userId, resultDetails, operatorId);
    }

    /**
     * 同步用户项目级别结果
     */
    private void syncUserProjectResult(
        String orgId, String xpdId, String userId, CaliDimResultWrapDto resultDetails, String operatorId) {
        if (resultDetails == null || StringUtils.isBlank(resultDetails.getXpdLevelId())) {
            log.debug("用户{}没有项目级别结果数据，跳过同步", userId);
            return;
        }

        // 查询现有的用户项目结果记录
        XpdResultUserPO existingResult = xpdResultUserMapper.findByUserId(orgId, xpdId, userId);

        if (existingResult != null) {
            // 更新现有记录
            existingResult.setXpdLevelId(resultDetails.getXpdLevelId());
            existingResult.setUpdateUserId(operatorId);
            existingResult.setUpdateTime(LocalDateTime.now());
            existingResult.setCaliFlag(1); // 标记为已校准
            // 原始快照
            existingResult.buildSnapshot();
            xpdResultUserMapper.updateByPrimaryKey(existingResult);
            log.debug("LOG21613:更新了用户{}的项目级别结果，等级ID={}", userId, resultDetails.getXpdLevelId());
        } else {
            log.debug("LOG21603:未找到用户{}的项目级别结果记录，生成一个新的", userId);
            XpdResultUserPO po = new XpdResultUserPO();
            po.setOrgId(orgId);
            po.setXpdId(xpdId);
            po.setUserId(userId);
            po.setXpdLevelId(resultDetails.getXpdLevelId());
            po.setCompetent(isCompetent(orgId, xpdId, resultDetails.getXpdLevelId()));
            po.setUpdateUserId(operatorId);
            po.setUpdateTime(LocalDateTime.now());
            po.setCaliFlag(1); // 标记为已校准
            po.setOriginalSnap(null); // 新增的记录之前没有盘点结果，无法记录快照
            xpdResultUserMapper.insert(po);
        }
    }

    /**
     * 判断用户是否达标
     */
    private Integer isCompetent(String orgId, String xpdId, String xpdLevelId) {
        List<XpdLevelPO> xpdLevels = xpdLevelMapper.selectByXpdId(orgId, xpdId);
        for (XpdLevelPO xpdLevel : xpdLevels) {
            if (xpdLevel.getId().equals(xpdLevelId)) {
                return xpdLevel.getCompetent();
            }
        }
        log.debug("LOG21593:{},{},{}", orgId, xpdId, xpdLevelId);
        return 0;
    }

    /**
     * 完整同步用户维度结果（先清理后同步）
     */
    private void syncCompleteUserDimResults(
        String orgId, String xpdId, String userId, CaliDimResultWrapDto resultDetails, String operatorId) {
        if (resultDetails == null) {
            log.debug("用户{}没有结果详情数据，跳过维度结果同步", userId);
            return;
        }

        // 1. 查询用户现有的所有维度结果
        List<XpdResultUserDimPO> existingRecords = xpdResultUserDimMapper.findByXpdIdAndUserId(orgId, xpdId, userId);
        Map<String, XpdResultUserDimPO> existingMap = existingRecords.stream()
            .collect(Collectors.toMap(XpdResultUserDimPO::getSdDimId, Function.identity()));

        // 2. 从resultDetails中获取应该保留的维度ID
        Set<String> targetDimIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(resultDetails.getDimResults())) {
            targetDimIds = resultDetails.getDimResults().stream()
                .map(CaliDimResultDto::getSdDimId)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        }
        final Set<String> effectivelyFinalTargetDimIds = targetDimIds;

        // 3. 清理不在resultDetails中的旧维度结果
        List<String> toDeleteIds = existingRecords.stream()
            .filter(record -> !effectivelyFinalTargetDimIds.contains(record.getSdDimId()))
            .map(XpdResultUserDimPO::getId)
            .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(toDeleteIds)) {
            // 逻辑删除不在resultDetails中的维度结果
            List<XpdResultUserDimPO> toDelete = toDeleteIds.stream()
                .map(id -> {
                    XpdResultUserDimPO po = new XpdResultUserDimPO();
                    po.setId(id);
                    po.setDeleted(1);
                    po.setUpdateUserId(operatorId);
                    po.setUpdateTime(LocalDateTime.now());
                    return po;
                })
                .collect(Collectors.toList());
            xpdResultUserDimMapper.batchUpdateResult(toDelete);
            log.debug("LOG21623:清理了用户{}的旧维度:{}", userId, toDeleteIds);
        }

        // 4. 同步resultDetails中的维度结果
        if (CollectionUtils.isNotEmpty(resultDetails.getDimResults())) {
            List<XpdResultUserDimPO> toUpdate = new ArrayList<>();

            for (CaliDimResultDto dimResult : resultDetails.getDimResults()) {
                if (StringUtils.isBlank(dimResult.getSdDimId())) {
                    continue;
                }

                XpdResultUserDimPO existing = existingMap.get(dimResult.getSdDimId());
                if (existing != null && existing.getDeleted() == 0) {
                    // 更新现有记录
                    existing.buildSnapshot(); // 保存原始快照
                    if (StringUtils.isNotBlank(dimResult.getGridLevelId())) {
                        existing.setGridLevelId(dimResult.getGridLevelId());
                    }
                    if (dimResult.getScoreValue() != null) {
                        existing.setScoreValue(dimResult.getScoreValue());
                    }
                    if (dimResult.getQualifiedPtg() != null) {
                        existing.setQualifiedPtg(dimResult.getQualifiedPtg());
                    }
                    existing.setUpdateUserId(operatorId);
                    existing.setUpdateTime(LocalDateTime.now());
                    existing.setCaliFlag(1); // 标记为已校准
                    toUpdate.add(existing);
                }
            }

            if (CollectionUtils.isNotEmpty(toUpdate)) {
                xpdResultUserDimMapper.batchUpdateResult(toUpdate);
                log.debug("LOG21633:更新了用户{}的{}个维度结果", userId, toUpdate.size());
            }
        }
    }

    /**
     * 完整同步用户指标结果（先清理后同步）
     */
    private void syncCompleteUserIndicatorResults(
        String orgId, String xpdId, String userId, CaliDimResultWrapDto resultDetails, String operatorId) {
        // 注意：指标结果通常不在CaliDimResultWrapDto中，而是在caliDetails中
        // 这里主要是为了保持接口一致性，实际的指标同步逻辑可能需要从其他地方获取数据
        log.debug("用户{}的指标结果同步（当前版本暂不处理指标结果的完整同步）", userId);

        // TODO: 如果需要处理指标结果的完整同步，需要：
        // 1. 查询用户现有的所有指标结果
        // 2. 从校准结果中获取应该保留的指标ID
        // 3. 清理不在校准结果中的旧指标结果
        // 4. 同步校准结果中的指标结果
    }

    /**
     * 同步用户维度组结果
     */
    private void syncUserDimCombResults(
        String orgId, String xpdId, String userId, CaliDimResultWrapDto resultDetails, String operatorId) {
        List<XpdDimCombPO> dimCombs = xpdDimCombMapper.listByXpdId(orgId, xpdId);
        if (CollectionUtils.isEmpty(dimCombs)) {
            log.info("LOG21673:项目{}没有配置维度组，无需同步维度组结果", xpdId);
            return;
        }

        Map<String, CaliDimResultDto> dimResultMap = resultDetails.getDimResults().stream()
            .collect(Collectors.toMap(CaliDimResultDto::getSdDimId, Function.identity()));

        // 调用计算器计算结果
        List<XpdResultUserDimcombPO> newResults = userDimCombResultCalculator.calculateUserDimCombResults(
            orgId, xpdId, userId, dimCombs, dimResultMap, operatorId);

        if (CollectionUtils.isEmpty(newResults)) {
            log.info("LOG21678:用户{}在项目{}中没有计算出任何维度组结果", userId, xpdId);
            // 如果没有新结果，可能需要清理旧结果，或者根据业务逻辑决定是否清理
            // 此处暂时保留原逻辑，即只在有新结果时进行同步
            // xpdResultUserDimcombMapper.deleteByUserIdAndXpdId(orgId, xpdId, userId);
            return;
        }

        // 查询用户已有的维度组结果
        List<XpdResultUserDimcombPO> existingResults = xpdResultUserDimcombMapper.findByXpdIdAndUserIds(
            orgId, xpdId,
            Collections.singletonList(userId));

        syncDimCombResultsIncremental(existingResults, newResults, userId, operatorId); // Pass operatorId
    }

    /**
     * 增量同步维度组结果
     */
    private void syncDimCombResultsIncremental(
        List<XpdResultUserDimcombPO> existingResults,
        List<XpdResultUserDimcombPO> newResults, String userId, String operatorId) { // Added operatorId

        // 构建现有结果的映射表，key为维度组ID
        Map<String, XpdResultUserDimcombPO> existingMap = existingResults.stream()
            .collect(Collectors.toMap(XpdResultUserDimcombPO::getDimCombId, Function.identity()));

        // 构建新结果的映射表，key为维度组ID
        Map<String, XpdResultUserDimcombPO> newMap = newResults.stream()
            .collect(Collectors.toMap(XpdResultUserDimcombPO::getDimCombId, Function.identity()));

        List<XpdResultUserDimcombPO> toInsert = new ArrayList<>();
        List<XpdResultUserDimcombPO> toUpdate = new ArrayList<>();
        List<String> toDeleteIds = new ArrayList<>();

        // 处理新结果：新增或更新
        for (XpdResultUserDimcombPO newResult : newResults) {
            String dimCombId = newResult.getDimCombId();
            XpdResultUserDimcombPO existing = existingMap.get(dimCombId);

            if (existing == null) {
                // 新增 - newResult
                toInsert.add(newResult);
            } else {
                // 更新：保留原有的ID和创建信息，更新其他字段
                existing.buildSnapshot();
                existing.setCellId(newResult.getCellId());
                existing.setCellIndex(newResult.getCellIndex());
                existing.setUpdateUserId(operatorId);
                existing.setUpdateTime(LocalDateTime.now());
                existing.setCaliFlag(1);
                toUpdate.add(existing);
            }
        }

        // 处理需要删除的结果：存在于现有结果但不在新结果中
        for (XpdResultUserDimcombPO existingResult : existingResults) {
            if (!newMap.containsKey(existingResult.getDimCombId())) {
                toDeleteIds.add(existingResult.getId());
            }
        }

        // 执行数据库操作
        if (!toInsert.isEmpty()) {
            xpdResultUserDimcombMapper.batchInsert(toInsert);
            log.debug("LOG21723:用户{}新增{}个维度组结果", userId, toInsert.size());
        }

        if (!toUpdate.isEmpty()) {
            xpdResultUserDimcombMapper.batchInsertOrUpdate(toUpdate);
            log.debug("LOG21733:用户{}更新{}个维度组结果", userId, toUpdate.size());
        }

        if (!toDeleteIds.isEmpty()) {
            List<XpdResultUserDimcombPO> toDeleteEntities = toDeleteIds.stream().map(id -> {
                XpdResultUserDimcombPO po = new XpdResultUserDimcombPO();
                po.setId(id);
                po.setDeleted(1);
                po.setUpdateUserId(operatorId);
                po.setUpdateTime(LocalDateTime.now());
                return po;
            }).collect(Collectors.toList());
            xpdResultUserDimcombMapper.updateBatch(toDeleteEntities);
            log.debug("LOG21743:用户{}删除{}个维度组结果", userId, toDeleteIds.size());
        }
    }

}
