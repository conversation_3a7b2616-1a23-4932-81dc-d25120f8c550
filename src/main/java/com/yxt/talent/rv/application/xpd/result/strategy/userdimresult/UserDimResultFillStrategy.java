package com.yxt.talent.rv.application.xpd.result.strategy;

import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdUserDimResultsVO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.XpdGridPO;

import java.util.List;
import java.util.Map;

/**
 * 用户维度结果填充策略接口
 * 定义不同数据源填充用户维度结果的策略
 */
public interface UserDimResultFillStrategy {

    /**
     * 填充用户维度结果
     *
     * @param orgId 机构ID
     * @param xpdId 盘点项目ID
     * @param userIds 用户ID列表
     * @param xpdGrid 宫格信息
     * @param userInfoMap 用户信息映射
     */
    void fillUserDimResults(String orgId, String xpdId, List<String> userIds,
                           XpdGridPO xpdGrid, Map<String, XpdUserDimResultsVO> userInfoMap);

    /**
     * 获取策略类型
     *
     * @return 策略类型
     */
    UserDimResultFillStrategyType getStrategyType();
}
