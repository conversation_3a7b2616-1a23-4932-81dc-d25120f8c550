package com.yxt.talent.rv.application.xpd.result.strategy.userdimcombresult;

import com.yxt.talent.rv.model.calimeet.CaliDimResultDto;
import com.yxt.talent.rv.model.xpd.XpdDimCombPO;
import com.yxt.talent.rv.model.xpd.XpdResultUserDimcombPO;

import java.util.List;
import java.util.Map;

/**
 * 计算用户维度组结果策略接口
 */
public interface UserDimCombResultStrategy {

    /**
     * 计算用户在各个维度组中的结果
     *
     * @param orgId        组织ID
     * @param xpdId        项目ID
     * @param userId       用户ID
     * @param dimCombs     维度组列表
     * @param dimResultMap 维度结果映射
     * @param operatorId   操作人ID
     * @return 用户维度组结果列表
     */
    List<XpdResultUserDimcombPO> calculateUserDimCombResults(
            String orgId, String xpdId, String userId, List<XpdDimCombPO> dimCombs,
            Map<String, CaliDimResultDto> dimResultMap, String operatorId);
}
