package com.yxt.talent.rv.application.xpd.common.dto;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd.*;
import lombok.Builder;
import lombok.Data;
import org.assertj.core.util.Lists;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/12/10 16:29
 */
@Data
@Builder
public class XpdGridCombDto {

    private XpdGridPO newXpdGrid;
    private String delXpdGridId;

    @Builder.Default()
    private List<XpdGridCellPO> newGridCellList = Lists.newArrayList();
    @Builder.Default()
    private List<XpdDimCombPO> newDimCombList = Lists.newArrayList();
    @Builder.Default()
    private List<String> delDimCombIds = Lists.newArrayList();
    @Builder.Default()
    private List<XpdGridDimCombPO> newGridDimCombList = Lists.newArrayList();
    @Builder.Default()
    private List<XpdGridLevelPO> newGridLevelList = Lists.newArrayList();
    @Builder.Default()
    private List<XpdGridRatioPO> newGridRatioList = Lists.newArrayList();
    @Builder.Default()
    private Map<String, String> idMap = new HashMap<>();
}
