package com.yxt.talent.rv.application.xpd.result.strategy.userdimcombresult;

import com.yxt.talent.rv.model.calimeet.CaliDimResultDto;
import com.yxt.talent.rv.model.xpd.XpdDimCombPO;
import com.yxt.talent.rv.model.xpd.XpdResultUserDimcombPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 用户维度组结果计算策略上下文
 */
@Component
@RequiredArgsConstructor
public class UserDimCombResultStrategyContext {

    private final UserDimCombResultStrategy userDimCombResultStrategy;

    public List<XpdResultUserDimcombPO> calculateUserDimCombResults(
            String orgId, String xpdId, String userId, List<XpdDimCombPO> dimCombs,
            Map<String, CaliDimResultDto> dimResultMap, String operatorId) {
        return userDimCombResultStrategy.calculateUserDimCombResults(orgId, xpdId, userId, dimCombs, dimResultMap, operatorId);
    }
}
