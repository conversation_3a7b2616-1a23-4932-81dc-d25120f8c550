package com.yxt.talent.rv.application.xpd.report.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class XpdRpBaseDictDTO {

    @Schema(description = "id")
    private String id;
    @Schema(description = "编号")
    private String num;
    @Schema(description = "名称")
    private String name;
    @Schema(description = "等级")
    private int level;
    @Schema(description = "是否启用(0-否,1-是)")
    private int enabled;
    @Schema(description = "等级")
    private List<XpdRpBaseLevelDTO> levels;
    @Schema(description = "父id")
    private String parentId;
}
