package com.yxt.talent.rv.application.xpd.common.dto;

import com.yxt.talent.rv.controller.manage.xpd.rule.command.DimRuleDetails4Update;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 新盘点-项目规则配置入参Bean
 *
 * <AUTHOR>
 * @date 2024/12/6 11:35
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
@Slf4j
@ToString(callSuper = true)
public class XpdRuleConfDto {

    @Schema(description = "盘点项目Id", hidden = true)
    @NotBlank
    private String xpdId;

    @Schema(description = "盘点项目规则Id，创建时为空，编辑时必传")
    private String ruleConfId;

    @Schema(description = "盘点项目规则版本号，创建时为空，编辑时必传")
    private Integer ruleConfVersion;

    @Schema(description = "盘点维度列表")
    @NotEmpty
    private List<DimSimpleInfoDto> dimList;

    @Schema(description = "宫格模板Id")
    private String gridId;

    @Schema(description = "盘点结果类型 0：分值 1：达标率")
    private Integer resultType = 0;

    @Schema(description = "分制 0：原始分值 1：五分制 2：十分制")
    private Integer scoreSystem = 0;

    //    @Schema(description = "快速生成规则", hidden = true)
    //    private XpdRuleConfFastDto ruleConfFastDto;

    public XpdRuleConfDto buildForAudit(String xpdId) {
        this.xpdId = xpdId;
        return this;
    }
}
