package com.yxt.talent.rv.application.prj.dim.legacy;

import com.yxt.common.exception.ApiException;
import com.yxt.common.util.ApiUtil;
import com.yxt.talent.rv.controller.manage.prj.dim.command.DimSameModelAddCmd;
import com.yxt.talent.rv.controller.manage.prj.dim.command.DimToolTypeAddCmd;
import com.yxt.talent.rv.domain.prj.entity.conf.PrjDimConfTool;
import com.yxt.talent.rv.infrastructure.common.utilities.util.EntityUtil;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.dim.PrjDimConfToolMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.dim.PrjDimConfToolPO;
import com.yxt.talent.rv.infrastructure.service.remote.SpevalAclService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@Deprecated(since = "5.2")
@RequiredArgsConstructor
public class PrjDimConfToolAppService {

    private final PrjDimConfToolMapper prjDimConfToolMapper;
    private final SpevalAclService spevalAclService;

    /**
     * 删除配置工具
     *
     * @param orgId    机构id
     * @param configId 维度配置id
     * @param evalId   测评id
     */
    public void deleteConfigTool(String orgId, String configId, String evalId) {
        PrjDimConfToolPO prjDimConfTool = prjDimConfToolMapper.selectByOrgIdAndDimConfIdAndToolId(orgId, configId,
                evalId);
        if (prjDimConfTool != null) {
            prjDimConfToolMapper.deleteBatch(orgId, List.of(prjDimConfTool.getId()));
        }
    }

    /**
     * 删除配置工具
     *
     * @param orgId    机构id
     * @param configId 维度配置id
     */
    public void deleteConfigAllTool(String orgId, String configId) {
        List<PrjDimConfToolPO> prjDimConfTool = prjDimConfToolMapper.selectByOrgIdAndDimConfId(orgId, configId);
        if (CollectionUtils.isNotEmpty(prjDimConfTool)) {
            List<String> ids = prjDimConfTool.stream().map(PrjDimConfToolPO::getId).collect(Collectors.toList());
            prjDimConfToolMapper.deleteBatch(orgId, ids);
        }
    }

    /**
     * 盘点维度绑定工具
     *
     * @param dimToolTypeAddCmd 工具信息
     * @param userId            用户id
     * @param orgId             机构id
     */
    public void saveConfigTool(DimToolTypeAddCmd dimToolTypeAddCmd, String orgId, String userId, String projectId) {
        if (StringUtils.isBlank(dimToolTypeAddCmd.getToolId())) {
            return;
        }
        PrjDimConfToolPO prjDimConfTool = prjDimConfToolMapper.selectByOrgIdAndDimConfIdAndToolId(orgId,
                dimToolTypeAddCmd.getDimensionConfigId(), dimToolTypeAddCmd.getToolId());
        if (prjDimConfTool != null) {
            return;
        }
        prjDimConfTool = new PrjDimConfToolPO();
        prjDimConfTool.setId(ApiUtil.getUuid());
        prjDimConfTool.setOrgId(orgId);
        prjDimConfTool.setProjectId(projectId);
        prjDimConfTool.setDimensionConfigId(dimToolTypeAddCmd.getDimensionConfigId());
        prjDimConfTool.setToolId(dimToolTypeAddCmd.getToolId());
        prjDimConfTool.setToolType(dimToolTypeAddCmd.getRvType());
        prjDimConfTool.setToolSource(dimToolTypeAddCmd.getToolSource());
        EntityUtil.setAuditFields(prjDimConfTool, userId);
        prjDimConfToolMapper.insertOrUpdate(prjDimConfTool);
    }

    /**
     * 删除配置工具
     *
     * @param orgId    机构id
     * @param configId 维度配置id
     */
    public void deleteConfigTool(String orgId, String configId) {
        List<String> configIds = new ArrayList<>();
        configIds.add(configId);
        List<PrjDimConfToolPO> prjDimConfTool = prjDimConfToolMapper.selectEvalByOrgIdAndDimConfIds(orgId, configIds);
        List<String> ids = prjDimConfTool.stream().map(PrjDimConfToolPO::getId).collect(Collectors.toList());
        prjDimConfToolMapper.deleteBatch(orgId, ids);
    }

    /**
     * 盘点维度绑定工具
     *
     * @param sameModel 工具信息
     * @param userId    用户id
     * @param orgId     机构id
     */
    public void batchSaveConfigTool(DimSameModelAddCmd sameModel, String orgId, String userId, String projectId) {
        List<PrjDimConfToolPO> tools = prjDimConfToolMapper.selectByOrgIdAndDimConfIdAndToolSourceAndPrjId(orgId,
                projectId, sameModel.getDimensionConfigId(), 1);
        List<String> ids = tools.stream().map(PrjDimConfToolPO::getId).collect(Collectors.toList());
        prjDimConfToolMapper.deleteBatch(orgId, ids);

        List<PrjDimConfToolPO> configTools = new ArrayList<>();
        sameModel.getToolInfos().forEach(toolInfo -> {
            if (StringUtils.isNotBlank(toolInfo.getToolId())) {
                PrjDimConfToolPO prjDimConfTool = new PrjDimConfToolPO();
                prjDimConfTool.setId(ApiUtil.getUuid());
                prjDimConfTool.setOrgId(orgId);
                prjDimConfTool.setProjectId(projectId);
                prjDimConfTool.setDimensionConfigId(sameModel.getDimensionConfigId());
                prjDimConfTool.setToolId(toolInfo.getToolId());
                prjDimConfTool.setToolType(sameModel.getRvType());
                prjDimConfTool.setToolSource(sameModel.getToolSource());
                prjDimConfTool.setEvalRefCenter(toolInfo.getEvalRefCenter());
                EntityUtil.setAuditFields(prjDimConfTool, userId);
                configTools.add(prjDimConfTool);
            }
        });
        prjDimConfToolMapper.insertOrUpdateBatch(configTools);
    }

    public int sameModelRef(String orgId, String projectId, String toolId) {
        List<String> tooIds = new ArrayList<>();
        tooIds.add(toolId);
        List<PrjDimConfToolPO> prjDimConfTool = prjDimConfToolMapper.selectSameModelByOrgIdAndPrjIdAndToolIds(orgId,
                projectId, tooIds);
        if (CollectionUtils.isNotEmpty(prjDimConfTool)) {
            return 1;
        }
        return 0;
    }

    /**
     * 判断存在同模测评引用
     */
    public void checkExistSameModelRef(String orgId, String configId, String projectId) {
        List<PrjDimConfToolPO> prjDimConfTools = prjDimConfToolMapper.selectByOrgIdAndDimConfIdAndToolSourceAndPrjId(
                orgId, projectId, configId, 0);
        List<String> mapSelf = prjDimConfTools.stream().map(PrjDimConfToolPO::getToolId).collect(Collectors.toList());
        List<PrjDimConfToolPO> sameModelRef = prjDimConfToolMapper.selectEvalByOrgIdAndPrjIdAndToolSourceAndToolIds(
                orgId, projectId, 1, mapSelf);
        if (CollectionUtils.isNotEmpty(sameModelRef)) {
            throw new ApiException("apis.sptalentrv.prj.dim.tool.exist.same.model");
        }
    }

    public List<PrjDimConfToolPO> getSameRefEval(String orgId, String projectId, String toolId) {
        List<String> toolIds = new ArrayList<>();
        toolIds.add(toolId);
        return prjDimConfToolMapper.selectSameModelByOrgIdAndPrjIdAndToolIds(orgId, projectId, toolIds);
    }

    @Async
    public void deleteEvalByProjectId(String orgId, String projectId, String userId) {
        List<Integer> sourceType = new ArrayList<>(2);
        sourceType.add(PrjDimConfTool.Type.EVAL.getCode());
        sourceType.add(PrjDimConfTool.Type.BZ.getCode());
        // 只删除盘点自己创建的测评
        List<PrjDimConfToolPO> prjDimConfTools = prjDimConfToolMapper.selectByToolTypesOnlyRv(orgId,
                projectId, sourceType);
        if (CollectionUtils.isEmpty(prjDimConfTools)) {
            return;
        }
        // 检查测评是否在其他盘点项目中被使用
        List<String> toolIds = prjDimConfTools.stream()
                .filter(x -> x.getEvalRefCenter() == 0)
                .map(PrjDimConfToolPO::getToolId)
                .toList();


        List<String> useToolIds =
                prjDimConfToolMapper.findToolIdUseByDiffRv(orgId, projectId, toolIds);
        for (PrjDimConfToolPO prjDimConfTool : prjDimConfTools) {
            // 过滤掉被其他盘点使用的测评工具
            if (CollectionUtils.isNotEmpty(useToolIds) && useToolIds.contains(prjDimConfTool.getToolId())) {
                continue;
            }
            try {
                log.info("LOG61650:deleteEval evalId={}", prjDimConfTool.getToolId());
                spevalAclService.deleteEvaluation(prjDimConfTool.getToolId(), orgId, userId);
            } catch (Exception e) {
                log.error("LOG11745:", e);
            }
        }
    }

    @Async
    public void deleteEval(String orgId, String evalId, String userId) {
        log.info("LOG11280:deleteEval evalId={}", evalId);
        spevalAclService.deleteEvaluation(evalId, orgId, userId);
    }
}
