package com.yxt.talent.rv.controller.client.bizmgr.dept.viewobj;

import com.yxt.talent.rv.application.xpd.common.dto.XpdUserCntDTO;
import com.yxt.talent.rv.controller.manage.prj.prj.viewobj.PrjOverviewResultDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(name = "学员端-我的部门-部门项目-盘点项目聚合统计")
public class DeptXpdStatisticsClientVO extends XpdUserCntDTO {

    @Schema(description = "每个人才层级的人数")
    private List<PrjOverviewResultDTO> userResults;

}
