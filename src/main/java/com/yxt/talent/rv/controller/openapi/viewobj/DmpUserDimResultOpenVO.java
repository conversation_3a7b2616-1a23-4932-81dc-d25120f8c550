package com.yxt.talent.rv.controller.openapi.viewobj;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class DmpUserDimResultOpenVO {

    @Schema(description = "机构id, 字符长度：36")
    private String orgId;

    @Schema(description = "SaaS用户id, 字符长度：36")
    private String userId;

    @Schema(description = "第三方用户id，字符长度：100")
    private String thirdUserId;

    @Schema(description = "第三方部门id")
    private String thirdDeptId;

    @Schema(description = "第三方部门名称")
    private String thirdDeptName;

    @Schema(description = "维度映射到模板任职资格中的模版分类id，字符长度：36")
    private String cataId;

    @Schema(description = "维度映射到模板任职资格中的模版分类名称，字符长度：100")
    private String cataName;

    @Schema(description = "维度id，字符长度：36")
    private String dimId;

    @Schema(description = "维度名称，字符长度：800")
    private String dimName;

    @Schema(description = "是否达标，0-否，1-是")
    private Integer achieved;
}
