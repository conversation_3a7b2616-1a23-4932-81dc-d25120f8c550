package com.yxt.talent.rv.controller.facade.viewobj;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(name = "盘点项目和测训项目关联关系对象")
public class PrjTrainMapFacadeVO {

    @Schema(description = "盘点项目id")
    private String projectId;

    @Schema(description = "测训项目id")
    private String trainingId;

    @Schema(description = "盘点项目名称")
    private String projectName;
}
