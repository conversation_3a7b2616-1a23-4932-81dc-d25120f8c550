package com.yxt.talent.rv.controller.manage.prj.user.viewobj;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 测评评估人
 */
@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class EvalUserVO {
    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "姓名")
    private String fullname;

    @Schema(description = "登录名")
    private String username;

    @Schema(description = "部门")
    private String deptName;

    @Schema(description = "岗位")
    private String positionName;

    @Schema(description = "岗位id")
    private String orgId;

    private String positionId;
    private String managerId;
    private String managerFullname;
    private String deptManagerId;
    private String deptManagerFullname;

    @Schema(description = "自己评估")
    private EvalRelationVO selfRelation;

    @Schema(description = "上级评估")
    private EvalRelationVO leaderRelation;

    @Schema(description = "平级评估")
    private EvalRelationVO equalRelation;

    @Schema(description = "下级评估")
    private EvalRelationVO subordinateRelation;

    @Schema(description = "被测评人记录主键")
    private String evalUserId;

    @Schema(description = "方案维度人员数据")
    private List<EvalRelationVO> dimensionRelations;
}
