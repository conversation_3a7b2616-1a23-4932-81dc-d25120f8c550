package com.yxt.talent.rv.controller.manage.xpd.grid.command;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 *
 *
 * <AUTHOR>
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class XpdGridCreateCmd {

    private String xpdId;

    private String id;

    @Schema(description = "宫格名称")
    private String gridName;

    @Schema(description = "宫格类型,0-四宫格,1-九宫格,2-十六宫格")
    private Integer gridType;

    @Schema(description = "配置方式,0-统一配置,1-按维度组合配置")
    private Integer configType;

    @Schema(description = "维度组合")
    private List<XpdGridCombCmd> xpdGridCombCmds;

    @Schema(description = "描述")
    private String gridDesc;

}
