package com.yxt.talent.rv.controller.manage.perf.viewobj;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(name = "周期Id-name对象")
public class PerfPeriodVO {
    @Schema(description = "id")
    private String id;

    @Schema(description = "周期名称")
    private String periodName;

    @Schema(description = "绩效类型：0-月度绩效, 1-季度绩效, 2-半年度，3-年度")
    private Integer cycle;

    @Schema(description = "绩效年份：格式：YYYY")
    private Integer yearly;

    @Schema(description = "绩效周期：月度:1-12, 季度:1-4, 半年度:1-2,年份:20xx")
    private Integer period;

    @Schema(description = "绩效周期是否被引用，0否，1是")
    private Integer used = 0;

    @Schema(description = "绩效周期是否可编辑 0：不可编辑 1：可编辑")
    private Integer editable;

    @Schema(description = "绩效总分")
    private BigDecimal scoreTotal;
}
