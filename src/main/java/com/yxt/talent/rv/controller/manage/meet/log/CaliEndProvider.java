package com.yxt.talent.rv.controller.manage.meet.log;

import com.yxt.aom.base.entity.common.Activity;
import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.talent.rv.application.meet.NewCaliLogCommonService;
import com.yxt.talent.rv.application.xpd.xpd.XpdService;
import com.yxt.talent.rv.controller.manage.meet.viewobj.MeetOptStatusLogVO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetPO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/5/6
 */
@Component
@RequiredArgsConstructor
public class CaliEndProvider implements AuditLogDataProvider<String, MeetOptStatusLogVO> {
    private final NewCaliLogCommonService newCaliLogCommonService;
    private final XpdService xpdService;

    @Override
    public MeetOptStatusLogVO before(String param, AuditLogBasicBean logBasic) {
        return null;

    }

    @Override
    public MeetOptStatusLogVO after(String param, AuditLogBasicBean logBasic) {

        MeetOptStatusLogVO meetOptStatusLogVO = new MeetOptStatusLogVO();
        CalimeetPO calimeetPO = newCaliLogCommonService.getCliMeetPO(param, logBasic.getOrgId());
        if (Objects.isNull(calimeetPO)) {
            return null;
        }
        meetOptStatusLogVO.setMeetName(calimeetPO.getCalimeetName());
        meetOptStatusLogVO.setXpdId(calimeetPO.getXpdId());
        return meetOptStatusLogVO;
    }

    @Override
    public Pair<String, String> entityInfo(String param, MeetOptStatusLogVO beforeObj, MeetOptStatusLogVO afterObj,
            AuditLogBasicBean logBasic) {
        String xpdName = "";
        if (Objects.nonNull(afterObj)) {
            String xpdId = afterObj.getXpdId();
            if (org.apache.commons.lang3.StringUtils.isNotBlank(xpdId)) {
                Activity activity = xpdService.findAomPrjByAomId(logBasic.getOrgId(), xpdId);
                xpdName = activity.getActvName();
            }
        }
        return Pair.of(param, String.format("盘点-%s-结束校准任务", xpdName));
    }
}
