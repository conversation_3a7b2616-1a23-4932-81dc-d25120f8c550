package com.yxt.talent.rv.controller.manage.calimeet.viewobj;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 校准任务记录VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CaliMeetTaskRecordVO {

    @Schema(description = "会议内容")
    private String record;

    @Schema(description = "附件列表")
    private List<AttachmentVO> attachments;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AttachmentVO {
        @Schema(description = "附件ID")
        private String id;

        @Schema(description = "附件名称")
        private String name;

        @Schema(description = "附件URL")
        private String fullUrl;

    }
} 