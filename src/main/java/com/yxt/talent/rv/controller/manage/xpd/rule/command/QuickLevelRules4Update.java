package com.yxt.talent.rv.controller.manage.xpd.rule.command;

import com.yxt.spsdk.common.bean.SpRuleBean;
import com.yxt.talent.rv.controller.manage.xpd.rule.enums.ScoreSystemEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
@Schema(description = "分层规则更新Bean")
public class QuickLevelRules4Update implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

//    @Schema(description = "分层名称", requiredMode = Schema.RequiredMode.REQUIRED)
//    @NotBlank
//    private List<AmSlDrawer4ReqDTO> levelnameDto;

    @Schema(description = "分层ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank
    private String id;

    @Schema(description = "分层名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank
    private String levelname;

    @Schema(description = "人员占比", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull
    @Size(max = 100)
    private Integer levelratio;

    @Schema(description = "达标得分(原始得分)")
    @DecimalMax(value = "1000.00")
    private BigDecimal score;

    @Schema(description = "达标得分(五分制)")
    @DecimalMax(value = "5.00")
    private BigDecimal scoreFive;

    @Schema(description = "达标得分(十分制)")
    @DecimalMax(value = "10.00")
    private BigDecimal scoreTen;

    @Schema(description = "达标率")
    private Integer compliancerate;

    private SpRuleBean judgeRule;

    /**
     * 前端apass一个字段搞不定分制，所以加了三个字段
     */
    public BigDecimal findScoreByScoreSystem(Integer scoreSystem) {
        if(ScoreSystemEnum.isTenScale(scoreSystem)) {
            return scoreTen;
        }
        if (ScoreSystemEnum.isFiveScale(scoreSystem)) {
            return scoreFive;
        }
        return score;
    }

}
