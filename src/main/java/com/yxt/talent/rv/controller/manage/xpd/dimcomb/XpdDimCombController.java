package com.yxt.talent.rv.controller.manage.xpd.dimcomb;

import com.alibaba.fastjson2.JSONObject;
import com.yxt.auditlog.annotation.Auditing;
import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.api.GenericCommonData;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.ApiUtil;
import com.yxt.common.util.BeanCopierUtil;
import com.yxt.common.util.BeanHelper;
import com.yxt.modelhub.api.bean.dto.SearchDTO;
import com.yxt.modelhub.api.utils.QueryUtil;
import com.yxt.spsdk.audit.annotations.EasyAuditLog;
import com.yxt.talent.rv.application.xpd.dimcomb.XpdDimCombAppService;
import com.yxt.talent.rv.controller.manage.xpd.dimcomb.command.Dimcomb4Create;
import com.yxt.talent.rv.controller.manage.xpd.dimcomb.command.Dimcomb4Update;
import com.yxt.talent.rv.controller.manage.xpd.dimcomb.command.XpdDimCombCreateCmd;
import com.yxt.talent.rv.controller.manage.xpd.dimcomb.command.XpdDimCombPutCmd;
import com.yxt.talent.rv.controller.manage.xpd.dimcomb.query.XpdDimCombQuery;
import com.yxt.talent.rv.controller.manage.xpd.dimcomb.viewobj.Dimcomb4Get;
import com.yxt.talent.rv.controller.manage.xpd.dimcomb.viewobj.XpdDimCombListVO;
import com.yxt.talent.rv.controller.manage.xpd.dimcomb.viewobj.XpdDimCombVO;
import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.GridComb4Get;
import com.yxt.talent.rv.controller.manage.xpd.result.viewobj.XpdDimCombInfoVO;
import com.yxt.talent.rv.infrastructure.common.constant.RvAuditLogConstants;
import com.yxt.talent.rv.infrastructure.common.utilities.util.ApassEntityUtils;
import com.yxt.talent.rv.infrastructure.common.utilities.util.DateTimeUtil;
import com.yxt.talent.rv.infrastructure.config.swagger.SwaggerPageQuery;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.yxt.common.Constants.MEDIATYPE;
import static com.yxt.common.enums.AuthType.TOKEN;
import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.AUTH_CODE_ALL;
import static org.springframework.http.HttpStatus.OK;

@Slf4j
@RestController
@RequestMapping("/mgr/xpd/dimcomb")
@RequiredArgsConstructor
@Tag(name = "xpd-管理端-维度组合", description = "维度组合管理")
public class XpdDimCombController {

    private final XpdDimCombAppService xpdDimCombAppService;
    private final AuthService authService;

    @Operation(summary = "创建维度组合接口")
    @PostMapping(value = "/add", consumes = Constants.MEDIATYPE)
    @ResponseStatus(OK)
    @Auth(type = {TOKEN}, codes = {AUTH_CODE_ALL})
    @Auditing
    @EasyAuditLog(value = RvAuditLogConstants.DIM_COMB_ADD, paramExp = "#return.data")
    public GenericCommonData<String> create(HttpServletRequest request, @Validated @RequestBody Dimcomb4Create bean) {
        UserCacheBasic userCache = authService.getUserCacheBasic(request);
        String id = xpdDimCombAppService.create(userCache.getOrgId(), userCache.getUserId(), convertCreateBean(bean));
        return new GenericCommonData<>(id);
    }

    @Operation(summary = "更新维度组合接口")
    @PutMapping(value = "/{id}", consumes = Constants.MEDIATYPE)
    @Parameter(name = "id", description = "维度组合id", in = ParameterIn.PATH)
    @ResponseStatus(OK)
    @Auth(type = {TOKEN}, codes = {AUTH_CODE_ALL})
    @Auditing
    @EasyAuditLog(value = RvAuditLogConstants.DIM_COMB_EDIT, paramExp = "#id")
    public void update(HttpServletRequest request, @PathVariable String id,
            @Validated @RequestBody Dimcomb4Update bean) {
        UserCacheBasic userCache = authService.getUserCacheBasic(request);

        xpdDimCombAppService.update(userCache.getOrgId(), userCache.getUserId(), convertUpdateBean(id, bean));
    }

    @Operation(summary = "查询维度组合详情接口")
    @GetMapping(value = "/detail/{id}")
    @Parameter(name = "id", description = "维度组合id", in = ParameterIn.PATH)
    @ResponseStatus(OK)
    @Auth(type = {TOKEN}, codes = {AUTH_CODE_ALL})
    public Dimcomb4Get getDetail(HttpServletRequest request, @PathVariable String id) {
        UserCacheDetail userCache = authService.getUserCacheDetail();
        XpdDimCombVO combVO = xpdDimCombAppService.getDetail(userCache.getOrgId(), id, userCache.getLocale());

        Dimcomb4Get result = new Dimcomb4Get();
        result.setId(combVO.getId());
        result.setOrgId(combVO.getOrgId());
        result.setName(combVO.getCombName());
        result.setCombtype(combVO.getCombType().toString());
        result.setCombdesc(combVO.getCombDesc());

        JSONObject xdim = new JSONObject();
        xdim.put("id", combVO.getXSdDimId());
        xdim.put("name", combVO.getXSdDimName());
        List<Object> objs = new ArrayList<>();
        objs.add(xdim);
        result.setXsddimid(ApassEntityUtils.createAmSlDrawer4RespDTOList(objs));

        JSONObject ydim = new JSONObject();
        ydim.put("id", combVO.getYSdDimId());
        ydim.put("name", combVO.getYSdDimName());
        objs = new ArrayList<>();
        objs.add(ydim);
        result.setYsddimid(ApassEntityUtils.createAmSlDrawer4RespDTOList(objs));

        return result;
    }


    @Operation(summary = "删除维度组合接口")
    @DeleteMapping(value = "/{id}")
    @Parameter(name = "id", description = "维度组合id", in = ParameterIn.PATH)
    @ResponseStatus(OK)
    @Auditing
    @EasyAuditLog(value = RvAuditLogConstants.DIM_COMB_DEL, paramExp = "#id")
    @Auth(type = {TOKEN}, codes = {AUTH_CODE_ALL})
    public void delete(HttpServletRequest request, @PathVariable String id) {
        UserCacheBasic userCache = authService.getUserCacheBasic(request);
        xpdDimCombAppService.delete(userCache.getOrgId(), userCache.getUserId(), id);
    }

    @SwaggerPageQuery
    @Operation(summary = "维度组合列表")
    @PostMapping(value = "/pagelist", produces = MEDIATYPE)
    @Parameters(value = {@Parameter(name = "offset", description = "从第几条开始取 默认0", in = ParameterIn.QUERY),
            @Parameter(name = "limit", description = "每页多少条 默认20", in = ParameterIn.QUERY)})
    @ResponseStatus(OK)
    @Auth(type = {TOKEN}, codes = {AUTH_CODE_ALL})
    public PagingList<Dimcomb4Get> pagelist(HttpServletRequest request, @RequestBody SearchDTO bean) {
        UserCacheDetail userCache = authService.getUserCacheDetail();
        PageRequest pageRequest = ApiUtil.getPageRequest(request);
        XpdDimCombQuery query = convertPageDto(bean);
        String orgId = userCache.getOrgId();
        PagingList<XpdDimCombListVO> pageList = xpdDimCombAppService.getPageList(orgId, pageRequest, query);

        PagingList<Dimcomb4Get> result = new PagingList<>();
        BeanCopierUtil.copy(pageList, result);
        if (CollectionUtils.isEmpty(pageList.getDatas())) {
            return result;
        }
        List<Dimcomb4Get> datas = new ArrayList<>();
        pageList.getDatas().forEach(item -> {
            Dimcomb4Get dimcomb4Get = convertGetBean(item);
            datas.add(dimcomb4Get);
        });
        result.setDatas(datas);
        return result;
    }

    private XpdDimCombCreateCmd convertCreateBean(Dimcomb4Create bean) {
        XpdDimCombCreateCmd createBean = new XpdDimCombCreateCmd();
        createBean.setCombName(bean.getName());
        createBean.setXSdDimId(bean.getXsddimid().get(0).getId());
        createBean.setYSdDimId(bean.getYsddimid().get(0).getId());
        createBean.setCombDesc(bean.getCombdesc());
        return createBean;
    }

    private XpdDimCombPutCmd convertUpdateBean(String id, Dimcomb4Update bean) {
        XpdDimCombPutCmd updateBean = new XpdDimCombPutCmd();
        updateBean.setId(id);
        updateBean.setCombName(bean.getName());
        updateBean.setXSdDimId(bean.getXsddimid().get(0).getId());
        updateBean.setYSdDimId(bean.getYsddimid().get(0).getId());
        updateBean.setCombDesc(bean.getCombdesc());
        return updateBean;
    }

    private Dimcomb4Get convertGetBean(XpdDimCombListVO combVO) {
        Dimcomb4Get result = new Dimcomb4Get();
        BeanHelper.copyProperties(combVO, result);
        result.setId(combVO.getId());
        result.setOrgId(combVO.getOrgId());
        result.setName(combVO.getCombName());
        result.setCombtype(combVO.getCombType().toString());
        result.setCombdesc(combVO.getCombDesc());
        result.setCreateTime(DateTimeUtil.makeLocalDateTime2Date(combVO.getCreateTime()));
        result.setCreateUserId(
                ApassEntityUtils.createDrawer4UserRespDTO(combVO.getCreateUserName(), combVO.getCreateUserId()));

        JSONObject xdim = new JSONObject();
        xdim.put("id", combVO.getXSdDimId());
        xdim.put("name", combVO.getXSdDimName());
        List<Object> objs = new ArrayList<>();
        objs.add(xdim);
        result.setXsddimid(ApassEntityUtils.createAmSlDrawer4RespDTOList(objs));

        JSONObject ydim = new JSONObject();
        ydim.put("id", combVO.getYSdDimId());
        ydim.put("name", combVO.getYSdDimName());
        objs = new ArrayList<>();
        objs.add(ydim);
        result.setYsddimid(ApassEntityUtils.createAmSlDrawer4RespDTOList(objs));
        return result;
    }

    private XpdDimCombQuery convertPageDto(SearchDTO bean) {
        XpdDimCombQuery pageParam = new XpdDimCombQuery();
        QueryUtil.Search search = QueryUtil.parse(bean);
        Map<String, String> filterEqMap = search.getFilterEq();
        Map<String, List<String>> filterInMap = search.getFilterIn();
        QueryUtil.SearchQuery searchQuery = search.getSearchLike();
        pageParam.setCombName(searchQuery.getValue());

        pageParam.setXSdDimId(StringUtils.isBlank(filterEqMap.get("xsddimid")) ? null : filterEqMap.get("xsddimid"));
        pageParam.setYSdDimId(StringUtils.isBlank(filterEqMap.get("ysddimid")) ? null : filterEqMap.get("ysddimid"));
        pageParam.setXpdId(StringUtils.isBlank(filterEqMap.get("xpdId")) ? null : filterEqMap.get("xpdId"));
        return pageParam;
    }

    @Parameters(value = {@Parameter(name = "offset", description = "从第几条开始取 默认0", in = ParameterIn.QUERY),
            @Parameter(name = "limit", description = "每页多少条 默认20", in = ParameterIn.QUERY)})
    @Operation(summary = "项目内维度组合列表")
    @PostMapping(value = "/search", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = {AuthType.TOKEN})
    public PagingList<GridComb4Get> pageList(HttpServletRequest request, @RequestBody SearchDTO bean) {
        QueryUtil.Search search = QueryUtil.parse(bean);
        String xpdId = search.getFilterEq().get("xpdId");
        return xpdDimCombAppService.getDimCombPage(request, xpdId);
    }

    @ResponseStatus(OK)
    @Auth(codes = {AUTH_CODE_ALL})
    @Operation(summary = "根据项目维度组列表")
    @GetMapping(value = "/{xpdId}", produces = MEDIATYPE)
    public List<XpdDimCombInfoVO> getDimCombList(HttpServletRequest request, @PathVariable String xpdId) {
        UserCacheDetail currentUser = authService.getUserCacheDetail(request);
        return xpdDimCombAppService.getDimCombList(currentUser, xpdId);
    }

}
