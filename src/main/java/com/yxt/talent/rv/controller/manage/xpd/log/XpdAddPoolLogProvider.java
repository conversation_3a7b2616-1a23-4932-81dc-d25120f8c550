package com.yxt.talent.rv.controller.manage.xpd.log;

import com.alibaba.fastjson.JSON;
import com.yxt.aom.base.entity.common.Activity;
import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.talent.rv.application.xpd.xpd.XpdService;
import com.yxt.talent.rv.controller.manage.xpd.log.viewobj.XpdPoolLogDTO;
import com.yxt.talent.rv.controller.manage.xpd.log.viewobj.XpdTrainingLogDTO;
import com.yxt.talent.rv.controller.manage.xpd.user.command.XpdUserAddActionPlanCmd;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.udp.UdpLiteUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.udp.UdpLiteUserPO;
import com.yxt.talentbkfacade.bean.StrIdNameBean;
import com.yxt.talentbkfacade.service.TalentbkFacade;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@AllArgsConstructor
@Component
public class XpdAddPoolLogProvider implements AuditLogDataProvider<XpdUserAddActionPlanCmd, XpdPoolLogDTO> {

    private final UdpLiteUserMapper udpLiteUserMapper;
    private final TalentbkFacade talentbkFacade;


    @Override
    public XpdPoolLogDTO before(XpdUserAddActionPlanCmd bean, AuditLogBasicBean logBasic) {
        return null;
    }

    @Override
    public XpdPoolLogDTO after(XpdUserAddActionPlanCmd bean, AuditLogBasicBean logBasic) {
        log.info("XpdAddPoolLogProvider after={}", JSON.toJSONString(bean));
        XpdPoolLogDTO res = new XpdPoolLogDTO();
        res.setUsers(getUserString(logBasic.getOrgId(), bean.getUserIds()));

        if (CollectionUtils.isNotEmpty(bean.getTargetIds())) {
            List<StrIdNameBean> strIdNameBeans = talentbkFacade.queryPoolNameByIds(bean.getTargetIds());
            if (CollectionUtils.isNotEmpty(strIdNameBeans)) {
                res.setPoolName(strIdNameBeans.get(0).getName());
            }
        }


        return res;
    }

    private String getUserString(String orgId, List<String> userIds) {
        List<UdpLiteUserPO> tcList = udpLiteUserMapper.selectByUserIds(orgId, userIds);
        StringBuilder tcSb = new StringBuilder();
        tcList.forEach(a -> tcSb.append(a.getFullname())
            .append("（")
            .append(a.getUsername())
            .append("）")
            .append("，"));

        if (!tcSb.isEmpty()) {
            tcSb.delete(tcSb.length() - 1, tcSb.length());
        }

        return tcSb.toString();
    }

    @Override
    public Pair<String, String> entityInfo(
        XpdUserAddActionPlanCmd bean, XpdPoolLogDTO beforeObj, XpdPoolLogDTO afterObj,
            AuditLogBasicBean logBasic) {
        String name = String.format("盘点-%s-加入人才池", afterObj.getPoolName());
        return Pair.of(bean.getXpdId(), name);

    }
}
