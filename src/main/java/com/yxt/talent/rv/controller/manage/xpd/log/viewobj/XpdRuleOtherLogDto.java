package com.yxt.talent.rv.controller.manage.xpd.log.viewobj;

import com.yxt.spsdk.audit.annotations.AuditLogField;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Slf4j
@ToString(callSuper = true)
public class XpdRuleOtherLogDto {

    @AuditLogField(name = "计算方式", orderIndex = 1)
    private String calctype;

    @AuditLogField(name = "结果类型", orderIndex = 2)
    private String resulttype;

    @AuditLogField(name = "计算规则", orderIndex = 3)
    private String calcrule;

    @AuditLogField(name = "快捷配置规则", orderIndex = 4)
    private String ruleCalcListJson;

    @AuditLogField(name = "高级公式", orderIndex = 5)
    private String formuladisplay;

    @AuditLogField(name = "分层方式", orderIndex = 6)
    private String leveltype;

    @AuditLogField(name = "分层优先级", orderIndex = 7)
    private String levelpriority;

    @AuditLogField(name = "分层规则", orderIndex = 8)
    private String levelRuleListJson;

    @AuditLogField(name = "规则说明", orderIndex = 9)
    private String ruledesc;

}
