package com.yxt.talent.rv.controller.manage.dmp.command;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Description 动态加人
 *
 * <AUTHOR>
 * @Date 2024/4/29 14:58
 **/
@Data
public class DmpAutoGroupCmd {

    @Schema(description = "动态用户组id")
    private String groupId;

    @Schema(description = "动态用户组名称")
    private String groupName;


    private String dmpId;

    public DmpAutoGroupCmd buildForAudit(String dmpId) {
        this.dmpId = dmpId;
        return this;
    }
}
