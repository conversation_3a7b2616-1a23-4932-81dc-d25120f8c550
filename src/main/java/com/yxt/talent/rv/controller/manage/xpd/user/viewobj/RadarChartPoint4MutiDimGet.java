package com.yxt.talent.rv.controller.manage.xpd.user.viewobj;

import com.yxt.talent.rv.application.xpd.report.dto.XpdRpBaseDictDTO;
import com.yxt.talent.rv.application.xpd.report.dto.XpdRpBaseRtDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 多测评维度，展示雷达图点位
 *
 * <AUTHOR>
 * @since 2021-05-25 17:33:35
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(name = "雷达图点位，多维度展示")
public class RadarChartPoint4MutiDimGet {
    @Schema(description = "指标ID")
    private String id;

    @Schema(description = "指标名称")
    private String name;

    @Schema(description = "类型，0-普通、1-能力、2-技能、3-知识、4-任务  5-分类（暂未使用）")
    private Integer itemType;

    @Schema(description = "父id")
    private String parentId;
    @Schema(description = "标准等级")
    private Integer standardLevel;
    @Schema(description = "排序字段")
    private Integer orderIndex;
    @Schema(description = "所在维度id")
    private String requireBaseId;
    @Schema(description = "优秀等级")
    private Integer excellentLevel;
    @Schema(description = "知识掌握度字典ID")
    private String kngDictId;
    @Schema(description = "任务熟练度字典ID")
    private String rtDictId;
    @Schema(description = "知识掌握度")
    private String kngDictName;
    @Schema(description = "任务熟练度")
    private String rtDictName;

    @Schema(description = "指标名称")
    private String itemValue;

    @Schema(description = "指标库id，为方便统一字段取值准备")
    private String itemId;


    @Schema(description = "能力/技能/知识 详情")
    private XpdRpBaseDictDTO item;
    @Schema(description = "任务详情")
    private XpdRpBaseRtDTO rt;


    @Schema(description = "得分")
    private BigDecimal score;

    @Schema(description = "数据来源")
    private String datasource;

    @Schema(description = "总分")
    private BigDecimal totalScore;

    @Schema(description = "是否达标,字符串拼接")
    private String qualified;

    @Schema(description = "是否加入维度计算规则，1 是，0否")
    private Integer hasRule = 1;

    @Schema(description = "标准分")
    private BigDecimal stdScore;

    @Schema(description = "子类", example = "")
    private List<RadarChartPoint4MutiDimGet> childs = new ArrayList<>();

}
