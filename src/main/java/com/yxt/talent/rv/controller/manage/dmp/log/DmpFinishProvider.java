package com.yxt.talent.rv.controller.manage.dmp.log;

import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.DmpMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.DmpPO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

/**
 * @Description
 *
 * <AUTHOR>
 * @Date 2024/9/5 11:46
 **/
@Slf4j
@AllArgsConstructor
@Component
public class DmpFinishProvider implements AuditLogDataProvider<String, String> {

    private final DmpMapper dmpMapper;

    @Override
    public String before(String param, AuditLogBasicBean logBasic) {
        return "";
    }

    @Override
    public String after(String param, AuditLogBasicBean logBasic) {
        return "";
    }

    @Override
    public Pair<String, String> entityInfo(
            String param, String beforeObj, String afterObj, AuditLogBasicBean logBasic) {

        DmpPO dmpPO = dmpMapper.selectByOrgIdAndId(logBasic.getOrgId(), param);
        if (dmpPO == null) {
            return null;
        }
        String name = String.format("人岗匹配项目-%s-结束项目", dmpPO.getDmpName());
        return Pair.of(param, name);

    }
}
