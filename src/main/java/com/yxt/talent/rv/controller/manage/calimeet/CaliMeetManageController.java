package com.yxt.talent.rv.controller.manage.calimeet;

import com.yxt.auditlog.annotation.Auditing;
import com.yxt.common.Constants;
import com.yxt.common.annotation.Auth;
import com.yxt.common.enums.AuthType;
import com.yxt.common.pojo.api.GenericCommonData;
import com.yxt.common.pojo.api.PageRequest;
import com.yxt.common.pojo.api.PagingList;
import com.yxt.common.pojo.user.UserCacheBasic;
import com.yxt.common.pojo.user.UserCacheDetail;
import com.yxt.common.service.AuthService;
import com.yxt.common.util.ApiUtil;
import com.yxt.modelhub.api.bean.dto.SearchDTO;
import com.yxt.spsdk.audit.annotations.EasyAuditLog;
import com.yxt.talent.rv.application.calimeet.CaliMeetAppComponent;
import com.yxt.talent.rv.application.calimeet.CaliMeetAppService;
import com.yxt.talent.rv.application.calimeet.dto.Calibration4Create;
import com.yxt.talent.rv.application.calimeet.dto.Calibration4Get;
import com.yxt.talent.rv.application.calimeet.dto.Calibration4Update;
import com.yxt.talent.rv.infrastructure.common.constant.RvAuditLogConstants;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;


import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.CALI_MEET_ADD;
import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.CALI_MEET_DEL;
import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.CALI_MEET_END;
import static com.yxt.talent.rv.infrastructure.common.constant.AuthCodes.CALI_MEET_LIST;
import static org.springframework.http.HttpStatus.NO_CONTENT;

/**
 * 盘点校准会
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@Tag(name = "盘点校准会(新6.3)", description = "盘点校准会(新6.3)")
@RequestMapping(value = "/mgr/calimeet")
public class CaliMeetManageController {

    private final CaliMeetAppService caliMeetAppService;
    private final AuthService authService;
    private final CaliMeetAppComponent caliMeetAppComponent;

    @Parameters(value = {@Parameter(name = "offset", description = "从第几条开始取 默认0", in = ParameterIn.QUERY),
            @Parameter(name = "limit", description = "每页多少条 默认20", in = ParameterIn.QUERY)})
    @Operation(summary = "校准任务列表")
    @PostMapping(value = "/search", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    @Auth(action = Constants.LOG_TYPE_GETLIST, type = {AuthType.TOKEN}, codes = CALI_MEET_LIST)
    public PagingList<Calibration4Get> pageList(HttpServletRequest request, @RequestBody SearchDTO bean) {
        UserCacheDetail userCacheBasic = authService.getUserCacheDetail();
        PageRequest pageRequest = ApiUtil.getPageRequest(request);
        return caliMeetAppComponent.pageList(pageRequest, userCacheBasic.getOrgId(), bean, userCacheBasic.getUserId(),
                userCacheBasic.getAdmin());
    }

    @Operation(summary = "校准任务创建")
    @PostMapping(produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @Auth(action = Constants.LOG_TYPE_CREATESINGLE, type = {AuthType.TOKEN}, codes = CALI_MEET_ADD)
    @Auditing
    @EasyAuditLog(value = RvAuditLogConstants.CALI_MEET_ADD_NEW, paramExp = "#return.data")
    public GenericCommonData<String> add(@Validated @RequestBody Calibration4Create bean) {
        UserCacheBasic userCacheBasic = authService.getUserCacheBasic();
        return new GenericCommonData<>(
                caliMeetAppComponent.add(userCacheBasic.getOrgId(), userCacheBasic.getUserId(), bean));
    }


    @Parameter(name = "id", description = "校准任务id", in = ParameterIn.PATH)
    @Operation(summary = "校准任务详情")
    @GetMapping(value = "/{id}")
    @ResponseBody
    @Auth(action = Constants.LOG_TYPE_GETSINGLE, type = {AuthType.TOKEN}, codes = CALI_MEET_LIST)
    public Calibration4Get detail(@PathVariable String id) {
        UserCacheBasic userCacheBasic = authService.getUserCacheBasic();
        return caliMeetAppComponent.detail(userCacheBasic.getOrgId(), id);
    }

    @Parameter(name = "id", description = "校准任务id", in = ParameterIn.PATH)
    @Operation(summary = "校准任务详情（过滤字段）")
    @PostMapping(value = "/{id}")
    @ResponseBody
    @Auth(action = Constants.LOG_TYPE_GETSINGLE, type = {AuthType.TOKEN}, codes = CALI_MEET_LIST)
    public Calibration4Get infoDetail(@PathVariable String id, @RequestBody SearchDTO bean) {
        UserCacheBasic userCacheBasic = authService.getUserCacheBasic();

        return caliMeetAppComponent.detail(userCacheBasic.getOrgId(), id);
    }


    @Operation(summary = "校准任务编辑")
    @Parameter(name = "id", description = "校准任务id", in = ParameterIn.PATH)
    @PutMapping(value = "/{id}", consumes = MediaType.APPLICATION_JSON_VALUE)
    @Auth(action = Constants.LOG_TYPE_UPDATESINGLE, type = {AuthType.TOKEN}, codes = CALI_MEET_ADD)
    @Auditing
    @EasyAuditLog(value = RvAuditLogConstants.CALI_MEET_UPDATE_NEW, paramExp = "#id")
    public void edit(HttpServletRequest request, @PathVariable String id,
            @Validated @RequestBody Calibration4Update bean) {
        UserCacheDetail userCacheBasic = authService.getUserCacheDetail(request);
        String token = ApiUtil.getToken(request);
        caliMeetAppComponent.edit(userCacheBasic.getOrgId(), userCacheBasic.getUserId(), id, bean, token,
                userCacheBasic);
    }

    @Operation(summary = "删除会议")
    @DeleteMapping(value = "/{id}")
    @ResponseStatus(NO_CONTENT)
    @Auth(codes = CALI_MEET_DEL)
    @Auditing
    @EasyAuditLog(value = RvAuditLogConstants.CALI_DELETE_NEW, paramExp = "#id")
    public void deleteMeeting(HttpServletRequest request, @PathVariable String id) {
        UserCacheBasic operator = authService.getUserCacheBasic(request);
        caliMeetAppService.deleteMeeting(id, operator.getUserId(), operator.getOrgId());
    }

    @Operation(summary = "结束校准会")
    @PutMapping(value = "/close/{id}")
    @ResponseStatus(NO_CONTENT)
    @Auth(codes = CALI_MEET_END)
    @Auditing
    //@AuditingPlus(strategyClass = CaliMeetCloseAuditLogStrategy.class)
    @EasyAuditLog(value = RvAuditLogConstants.CALI_END, paramExp = "#id")
    public void closeMeeting(HttpServletRequest request, @PathVariable String id) {
        UserCacheDetail operator = authService.getUserCacheDetail(request);
        String token = ApiUtil.getToken(request);
        caliMeetAppService.closeMeeting(id, operator.getUserId(), operator.getOrgId(), operator, token);
    }

    @Operation(summary = "开启校准会")
    @PutMapping(value = "/open/{id}")
    @ResponseStatus(NO_CONTENT)
    @Auth(codes = CALI_MEET_END)
    @Auditing
    //@AuditingPlus(strategyClass = CaliMeetCloseAuditLogStrategy.class)
    @EasyAuditLog(value = RvAuditLogConstants.CALI_START, paramExp = "#id")
    public void openMeeting(HttpServletRequest request, @PathVariable String id) {
        UserCacheDetail operator = authService.getUserCacheDetail(request);
        String token = ApiUtil.getToken(request);
        caliMeetAppService.openMeeting(id, operator.getUserId(), operator.getOrgId(), operator, token);
    }

    @Operation(summary = "撤回校准会")
    @PutMapping(value = "/withdraw/{id}")
    @ResponseStatus(NO_CONTENT)
    @Auth(codes = CALI_MEET_END)
    @Auditing
    //@AuditingPlus(strategyClass = CaliMeetCloseAuditLogStrategy.class)
    @EasyAuditLog(value = RvAuditLogConstants.CALI_WITHDRAW, paramExp = "#id")
    public void withdrawMeeting(HttpServletRequest request, @PathVariable String id) {
        UserCacheDetail operator = authService.getUserCacheDetail(request);
        String token = ApiUtil.getToken(request);
        caliMeetAppService.withdrawMeeting(id, operator.getUserId(), operator.getOrgId(), operator, token);
    }


}
