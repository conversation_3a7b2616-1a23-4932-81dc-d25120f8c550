package com.yxt.talent.rv.controller.manage.dmp.viewobj.log;

import com.yxt.spsdk.audit.annotations.AuditLogField;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description 动态盘点删除人员
 *
 * <AUTHOR>
 * @Date 2024/8/29 16:52
 **/
@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class DmpDeleteUserLogDTO {

    @AuditLogField(name = "学员", orderIndex = 1)
    private String user;

}
