package com.yxt.talent.rv.controller.manage.common.log;

import com.yxt.spsdk.audit.base.AuditLogNoDataProvider;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.common.CategoryMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.common.CategoryPO;
import com.yxt.talent.rv.infrastructure.service.audit.AuditLogHelper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

/**
 * @Description
 *
 * <AUTHOR>
 * @Date 2024/9/5 10:06
 **/
@Slf4j
@AllArgsConstructor
@Component
public class CategoryDeleteProvider extends AuditLogNoDataProvider<Object> {


    private final CategoryMapper categoryMapper;

    @Override
    public Pair<String, String> entityInfo(Object param, AuditLogBasicBean logBasic) {
        String categoryId = (String) param;
        CategoryPO categoryPO = categoryMapper.selectByOrgIdAndId(logBasic.getOrgId(), categoryId);
        if (categoryPO == null) {
            return null;
        }
        String name = AuditLogHelper.Module.SETTING.getName() + "-盘点分类-" +
                      categoryPO.getCategoryName();
        return Pair.of(categoryId, name);
    }

}
