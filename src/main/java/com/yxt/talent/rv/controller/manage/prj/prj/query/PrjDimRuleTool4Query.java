package com.yxt.talent.rv.controller.manage.prj.prj.query;

import com.yxt.criteria.Query;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(name = "测评工具余量查询对象")
public class PrjDimRuleTool4Query implements Query {

    @Schema(description = "工具ids")
    private List<String> toolIds;
}
