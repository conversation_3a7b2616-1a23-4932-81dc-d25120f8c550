package com.yxt.talent.rv.controller.manage.perf.log;

import com.yxt.common.util.ApiUtil;
import com.yxt.spsdk.audit.base.AuditLogDataProvider;
import com.yxt.spsdk.audit.bean.AuditLogBasicBean;
import com.yxt.talent.rv.controller.manage.perf.command.PerfPeriodCmd;
import com.yxt.talent.rv.controller.manage.perf.viewobj.PerfPeriodAddLogDTO;
import com.yxt.talent.rv.infrastructure.service.audit.AuditLogHelper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

/**
 * @Description 绩效周期新增
 *
 * <AUTHOR>
 * @Date 2024/9/3 14:58
 **/
@Slf4j
@AllArgsConstructor
@Component
public class PerfPeriodAddProvider implements AuditLogDataProvider<PerfPeriodCmd, PerfPeriodAddLogDTO> {

    @Override
    public PerfPeriodAddLogDTO before(PerfPeriodCmd param, AuditLogBasicBean logBasic) {
        return null;
    }

    @Override
    public PerfPeriodAddLogDTO after(PerfPeriodCmd param, AuditLogBasicBean logBasic) {
        PerfPeriodAddLogDTO dto = new PerfPeriodAddLogDTO();
        dto.setPeriodName(param.getPeriodName());
        return dto;
    }

    @Override
    public Pair<String, String> entityInfo(
            PerfPeriodCmd param, PerfPeriodAddLogDTO beforeObj, PerfPeriodAddLogDTO afterObj,
            AuditLogBasicBean logBasic) {

        String name = AuditLogHelper.Module.PERFORMANCE.getName() + "-" + param.getPeriodName();

        return Pair.of(ApiUtil.getUuid(), name);

    }
}
