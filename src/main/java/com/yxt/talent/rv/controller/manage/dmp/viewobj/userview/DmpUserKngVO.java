package com.yxt.talent.rv.controller.manage.dmp.viewobj.userview;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description 知识技能
 *
 * <AUTHOR>
 * @Date 2024/4/25 18:03
 **/
@Data
public class DmpUserKngVO {

    @Schema(description = "id")
    private String dimId;

    @Schema(description = "维度项目")
    private String dimItem;


    @Schema(description = "维度名称")
    private String dimName;

    @Schema(description = "任务类型 0-动态匹配 1-表单评价")
    private Integer taskType = 0;

    @Schema(description = "评估结果")
    private BigDecimal result;

    @Schema(description = "0 不达标，1达标")
    private Integer matched;
}
