package com.yxt.talent.rv.controller.manage.dmp.viewobj.userview;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description
 *
 * <AUTHOR>
 * @Date 2024/4/28 14:56
 **/
@Data
public class DmpUserDimResultVO {

    @Schema(description = "是否达标：0-不达标 1-达标")
    private Integer matched;

    private String skillLevelId;

    private BigDecimal formScore;

    private BigDecimal score;

    private BigDecimal weightScore;

    private String jqDimId;

    private String jqDimName;

    private String jqCataId;

    private String jqCataName;

    @Schema(description = "0-动态匹配 1-表单评价")
    private Integer taskType;

    @Schema(description = "维度类型：1、指标 2、标签 3、普通文本 4、能力模型，5、任务模型，6、资格证书 7、知识点")
    private Integer dimType;
}
