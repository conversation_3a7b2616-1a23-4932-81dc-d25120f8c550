package com.yxt.talent.rv.controller.client.bizmgr.dept.viewobj;

import com.yxt.talent.rv.application.org.profile.dto.OrgProfileBaseInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Schema(name = "学员端-我的部门-组织画像-部门基础维度统计")
public class OrgProfileBasicClientVO {

    @Schema(description = "学历列表")
    private List<OrgProfileBaseInfoDTO> educations;

    @Schema(description = "司龄列表")
    private List<OrgProfileBaseInfoDTO> empdurs;

    @Schema(description = "年龄列表")
    private List<OrgProfileBaseInfoDTO> ages;

    @Schema(description = "序列人数分布列表")
    private List<OrgProfileBaseInfoDTO> seriesList;
}
