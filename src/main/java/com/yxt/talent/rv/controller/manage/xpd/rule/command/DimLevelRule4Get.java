package com.yxt.talent.rv.controller.manage.xpd.rule.command;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.yxt.modelhub.api.bean.dto.AmSlDrawer4RespDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
@Schema(description = "分层规则详情返回")
public class DimLevelRule4Get implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "主键;id主键")
    private String id;

    @Schema(description = "分层名称;名称")
    private String name;

    @Schema(description = "人员占比")
    private Integer levelratio;

    @Schema(description = "达标得分")
    private BigDecimal levelvalue;

    @Schema(description = "达标率")
    private Integer compliancerate;

    @Schema(description = "绩效结果;按照业务需求,返回应用的实体字段")
    @JsonProperty("@perfresults")
    private AmSlDrawer4RespDTO perfresults;
}
