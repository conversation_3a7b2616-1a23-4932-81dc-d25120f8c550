package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yxt.talent.rv.application.dmp.dto.DmpDTO;
import com.yxt.talent.rv.application.dmp.msg.dto.DmpStartRemindDTO;
import com.yxt.talent.rv.controller.client.bizmgr.dept.query.DeptProjectClientQuery;
import com.yxt.talent.rv.controller.client.bizmgr.dept.viewobj.DeptDmpClientVO;
import com.yxt.talent.rv.controller.client.general.dmp.viewobj.DmpClientVO;
import com.yxt.talent.rv.controller.manage.dmp.query.DmpQuery;
import com.yxt.talent.rv.controller.manage.dmp.viewobj.DmpVO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.DmpPO;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.ResultHandler;

import java.util.Collection;
import java.util.List;
import java.util.Set;

public interface DmpMapper {

    int insert(@Nonnull DmpPO entity);

    long insertOrUpdate(@Nonnull DmpPO entity);

    int updateById(@Nonnull DmpPO entity);

    @Nonnull
    IPage<DmpVO> selectPage(
            @Nonnull IPage<DmpVO> page, @Nonnull @Param("orgId") String orgId,
            @Nonnull @Param("search") DmpQuery search, @Param("dmpIds") List<String> dmpIds,
            @Nonnull @Param("userId") String userId, @Param("admin") int admin);

    @Nullable
    DmpPO selectByOrgIdAndId(@Nonnull @Param("orgId") String orgId, @Param("dmpId") String dmpId);

    int countByName(@Nonnull @Param("orgId") String orgId, @Param("dmpName") String dmpName);

    int countByPlanId(@Nonnull @Param("orgId") String orgId, @Param("planId") String planId);

    int countByDmpIdAndDmpName(
            @Nonnull @Param("orgId") String orgId, @Param("dmpId") String dmpId,
            @Param("dmpName") String dmpName);

    int deleteDmpById(DmpPO entity);

    /**
     * 根据状态查询所有项目
     *
     * @param status
     */
    @Nonnull
    List<DmpStartRemindDTO> listByStatus(@Param("status") Integer status);

    /**
     * 获取结束时间最晚的一条状态是（2-进行中，3-已暂停，4-已结束）且指定学员已经计算出结果的人岗匹配项目
     *
     * @param orgId
     * @param userId
     * @param isDemoOrg
     */
    @Nullable
    DmpPO selectLatestDmpByUserId(
            @Nonnull @Param("orgId") String orgId, @Nonnull @Param("userId") String userId,
            @Param("isDemoOrg") boolean isDemoOrg);

    @Nonnull
    List<DmpPO> list(@Param("criteria") DmpDTO criteria);

    /**
     * 查询所有进行中的dmp项目
     */
    @Nonnull
    List<DmpPO> listInProgressDmps();

    void updateDmpStatus(
            @Nonnull @Param("orgId") String orgId, @Param("dmpId") String dmpId,
            @Param("status") int status, @Nonnull @Param("userId") String userId);

    @Nonnull
    List<String> listNotStartOrgIds();

    @Nonnull
    List<String> listNotFinishOrgIds();

    Page<DmpClientVO> pagingUserProfiles(
            @Nonnull IPage<DmpClientVO> page, @Nonnull @Param("orgId") String orgId,
            @Param("dmpIds") Collection<String> dmpIds);

    @Nonnull
    List<DmpPO> listByDmpIds(
            @Nonnull @Param("orgId") String orgId, @Param("dmpIds") Collection<String> dmpIds);

    void selectAndHandle(ResultHandler<DmpPO> handler);

    /**
     * 撤回项目，更改状态
     *
     * @param dmpId
     * @param orgId
     * @param operator
     */
    void withdrawProject(
            @Param("dmpId") String dmpId, @Nonnull @Param("orgId") String orgId,
            @Param("operator") String operator);

    @Nonnull
    IPage<DeptDmpClientVO> selectMineDmps(
            @Param("page") IPage<DeptDmpClientVO> page, @Nonnull @Param("orgId") String orgId,
            @Nonnull @Param("userId") String userId,
            @Param("criteria") DeptProjectClientQuery criteria);

    @Nullable
    DeptDmpClientVO selectMineDmpStatistics(
            @Nonnull @Param("orgId") String orgId, @Nonnull @Param("userId") String userId,
            @Param("criteria") DeptProjectClientQuery criteria);

    @Nonnull
    Collection<DmpPO> selectByOrgIdAndIds(
            @Param("orgId") String orgId, @Param("dmpIds") Set<String> dmpIds);

    List<String> queryOrgIds();

    /**
     * 查询用户创建或者负责的dmp项目个数
     *
     * @param orgId
     * @param userId
     * @return
     */
    int countByUserScore(@Param("orgId") String orgId, @Param("userId") String userId);

    void transferResource(
            @Param("orgId") String orgId, @Param("fromUserId") String fromUserId,
            @Param("toUserId") String toUserId);

    List<DmpPO> selectByOrgId(@Param("orgId") String orgId);

    void batchInsert(@Param("list") Collection<DmpPO> list);
}
