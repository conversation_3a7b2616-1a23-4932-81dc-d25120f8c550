package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.dmp.task;

import com.yxt.talent.rv.application.dmp.dto.DmpItemNumDTO;
import com.yxt.talent.rv.application.dmp.task.expt.DmpDimStatisDTO;
import com.yxt.talent.rv.controller.manage.dmp.query.DmpTaskDimQuery;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.CommonMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.task.DmpTaskDimPO;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import lombok.NonNull;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@Mapper
public interface DmpTaskDimMapper extends CommonMapper<DmpTaskDimPO> {

    long insertOrUpdate(@Nonnull DmpTaskDimPO entity);

    int updateById(@Nonnull DmpTaskDimPO entity);

    int updateBatch(@Nonnull @Param("list") Collection<DmpTaskDimPO> list);

    int batchInsert(@Nonnull @Param("list") Collection<DmpTaskDimPO> list);

    default void batchUpdate(Collection<DmpTaskDimPO> list) {
        batchExecute(list, this::updateBatch);
    }

    default void insertBatch(Collection<DmpTaskDimPO> list) {
        this.batchExecute(list, this::batchInsert);
    }

    @Nullable
    DmpTaskDimPO selectByOrgIdAndId(
            @Nonnull @Param("orgId") String orgId, @Nonnull @Param("id") String id);

    @Nonnull
    List<DmpItemNumDTO> countItem(
            @Nonnull @Param("orgId") String orgId, @Param("dmpIds") List<String> dmpIds);

    @Nonnull
    default List<DmpTaskDimPO> selectByTaskId(
            @NonNull String orgId, @NonNull String dmpId, @NonNull String taskId) {
        return search(DmpTaskDimQuery.builder().orgId(orgId).dmpId(dmpId).taskId(taskId).build());
    }

    @Nonnull
    List<DmpTaskDimPO> search(@Param("criteria") DmpTaskDimQuery criteria);

    @Nonnull
    default List<DmpTaskDimPO> selectByDmpId(@NonNull String orgId, @NonNull String dmpId) {
        return search(DmpTaskDimQuery.builder().orgId(orgId).dmpId(dmpId).build());
    }

    /**
     * 查询项目中当前使用的所有任职资格维度id，用于任职资格选择时置灰
     *
     * @param orgId
     * @param dmpId
     * @param excludeTaskId
     */
    @Nonnull
    List<String> selectJqDimIds(
            @Nonnull @Param("orgId") String orgId, @Param("dmpId") String dmpId,
            @Param("excludeTaskId") String excludeTaskId);

    int countDmpDim(@Nonnull @Param("orgId") String orgId, @Param("dmpId") String dmpId);

    @Nonnull
    List<DmpTaskDimPO> selectByIds(
            @Nonnull @Param("orgId") String orgId, @Param("dimIds") List<String> dimIds);

    void updateDimRuleSnap(
            @Nonnull @Param("orgId") String orgId, @Nonnull @Param("id") String id,
            @Param("ruleSnap") String ruleSnap);

    @Nonnull
    List<DmpTaskDimPO> selectRefreshableDmpTaskDim(
            @Nonnull @Param("orgId") String orgId, @Param("dmpId") String dmpId);

    default void insertOrUpdateBatch(Collection<DmpTaskDimPO> entities) {
        this.forEachExecute(entities, this::insertOrUpdate);
    }

    List<DmpDimStatisDTO> selectStaticsByDimId(
            @Param("orgId") String orgId, @Param("dmpId") String dmpId);

    List<DmpTaskDimPO> selectByOrgId(@Param("orgId") String orgId);

}
