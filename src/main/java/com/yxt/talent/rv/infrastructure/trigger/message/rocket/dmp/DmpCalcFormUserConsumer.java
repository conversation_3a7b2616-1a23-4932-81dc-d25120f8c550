package com.yxt.talent.rv.infrastructure.trigger.message.rocket.dmp;

import com.yxt.event.EventPublisher;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import com.yxt.talent.rv.domain.dmp.event.DmpCalcFormUserMessageEvent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.CONSUMER_GROUP_PREFIX;
import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.TOPIC_SE_C_DMP_FORM_USER_CALCULATE;

/**
 * 人岗匹配项目-表单任务-计算完成了表单用户的维度匹配+胜任度+分层 测评会将完成表单的学员发送过来
 * DmpFormUserCalcEventListener负责定义消息消费的协议, 具体实现交由基础设置中的具体mq组件来实现,
 * 这里目前使用的是rocketmq,后续如果切换到其他mq组件,只需要修改注解即可
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(         consumerGroup = CONSUMER_GROUP_PREFIX + TOPIC_SE_C_DMP_FORM_USER_CALCULATE,         topic = TOPIC_SE_C_DMP_FORM_USER_CALCULATE,         consumeThreadNumber = 5, consumeTimeout = 30)
public class DmpCalcFormUserConsumer implements RocketMQListener<DmpCalcFormUserMessageEvent> {

    private final EventPublisher eventPublisher;

    @Override
    public void onMessage(DmpCalcFormUserMessageEvent message) {
        try {
            log.debug("LOG12865:{}", message);
            eventPublisher.publish(message);
        } catch (Throwable e) {
            log.error("LOG61030:", e);
        }
    }

}
