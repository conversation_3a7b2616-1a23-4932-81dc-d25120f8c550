package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.xpd;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.domain.RvBaseEntity;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.yxt.spsdk.democopy.DemoCopyConstants.UDP_USER_ID;

/**
 * 盘点用户指标结果
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@TableName("rv_xpd_result_user_indicator")
public class XpdResultUserIndicatorPO extends RvBaseEntity implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 主键id
     */
    private String id;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 盘点项目id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_ID)
    private String xpdId;

    /**
     * 指标id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPSD_INDICATOR_ID)
    private String sdIndicatorId;

    /**
     * 用户id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = UDP_USER_ID)
    private String userId;

    /**
     * 分值
     */
    private BigDecimal scoreValue;

    /**
     * 结果明细，json
     */
    private String resultDetail;

    /**
     * 是否删除:0-未删除,1-已删除
     */
    private Integer deleted;

    /**
     * 创建人
     */
    private String createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 执行计算批次号
     */
    private Integer calcBatchNo;

    /**
     * 是否达标
     */
    private Integer qualified;

    /**
     * 冗余的绩效活动周期明细，格式：周期1：A; 周期2：C
     */
    private String perfSummary;

    /**
     * 冗余的绩效活动评估结果(优秀/良好)id, 绩效维度且按绩效结果计算时有效，指向 rv_activity_perf_result_conf.id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_ACTV_PERF_RESULT_CONF_ID)
    private String perfResultId;

    /**
     * 维度结果id, rv_xpd_result_user_dim.id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_XPD_RESULT_USER_DIM_ID)
    private String resultDimId;

    /**
     * 是否被校准过，0-否 1-是
     */
    private Integer caliFlag;

    /**
     * 被校准结果覆盖之前的原始的计算出来的数据快照
     */
    private String originalSnap;
}