package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.rule;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.common.Constants;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 规则分级条件表
 */
@Slf4j
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@TableName("rv_rule_condition")
public class PrjDimRuleCondPO {
    // 主键
    @TableId
    private String id;

    // 机构id
    @TableField("org_id")
    private String orgId;

    // 盘点项目id
    @TableField("project_id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_PRJ_ID)
    private String projectId;

    // 维度id
    @TableField("dimension_id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_PRJ_DIM_ID)
    private String dimensionId;

    // 计算规则表主键id
    @TableField("rule_id")
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_PRJ_RULE_ID)
    private String ruleId = "";

    // 符号（-2-小于等于，-1-小于，0-等于，1-大于，2-大于等于）
    @TableField("symbol")
    private Integer symbol = 0;

    // 等级（1-低，2-中，3-高）
    @TableField("condition_level")
    private Integer conditionLevel;

    // 数值
    @TableField("symbol_value")
    private BigDecimal symbolValue = BigDecimal.ZERO;

    // 创建人主键
    @TableField("create_user_id")
    private String createUserId;

    // 创建时间
    @TableField("create_time")
    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private Date createTime;

    // 更新人主键
    @TableField("update_user_id")
    private String updateUserId;

    // 更新时间
    @TableField("update_time")
    @DateTimeFormat(pattern = Constants.SDF_YEAR2MILLSECOND)
    private Date updateTime;
}
