package com.yxt.talent.rv.infrastructure.repository.prj;

import com.yxt.talent.rv.domain.prj.PrjUser;
import com.yxt.talent.rv.domain.prj.entity.user.PrjUserImportResult;
import com.yxt.talent.rv.domain.prj.entity.user.PrjUserResult;
import com.yxt.talent.rv.domain.prj.repo.PrjUserDomainRepo;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.user.PrjUserImportResultMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.user.PrjUserMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.prj.user.PrjUserResultMapper;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.user.PrjUserImportResultPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.user.PrjUserPO;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.prj.user.PrjUserResultPO;
import com.yxt.talent.rv.infrastructure.repository.prj.assembler.PrjUserAssembler;
import jakarta.annotation.Nonnull;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.stream.Collectors;

import static com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil.filterNull;
import static com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil.mapListThenFilterNull;
import static com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil.mapSubList;
import static com.yxt.talent.rv.infrastructure.common.utilities.util.CommonUtil.mapSubListSub;

@Slf4j
@Repository
@RequiredArgsConstructor
public class PrjUserDomainRepoImpl implements PrjUserDomainRepo {

    private final PrjUserAssembler prjUserAssembler;
    private final PrjUserMapper prjUserMapper;
    private final PrjUserResultMapper prjUserResultMapper;
    private final PrjUserImportResultMapper prjUserImportResultMapper;

    @Nonnull
    @Override
    public Collection<PrjUser> loadByPrjId(
            @NonNull String orgId, @NonNull String prjId, @NonNull PrjUser.LoadConfig loadConfig) {
        List<PrjUserPO> prjUserPos = prjUserMapper.selectByPrjId(orgId, prjId);
        return prjUserPos.stream()
                .map(prjUserAssembler::toPrjUser)
                .filter(Objects::nonNull)
                .map(prjUser -> loadSub(prjUser, loadConfig))
                .collect(Collectors.toList());
    }

    @Override
    public Optional<PrjUser> load(
            @NonNull String orgId, @NonNull String entityId,
            @NonNull PrjUser.LoadConfig loadConfig) {
        return this.load(orgId, entityId).map(entity -> loadSub(entity, loadConfig));
    }

    @Override
    public Optional<PrjUser> load(@NonNull String orgId, @NonNull String entityId) {
        PrjUserPO data = prjUserMapper.selectByOrgIdAndId(orgId, entityId);
        return Optional.ofNullable(data).map(prjUserAssembler::toPrjUser);
    }

    @Override
    public void save(@NonNull PrjUser entity) {
        String orgId = entity.getOrgId();

        deleteConvertUpdate(orgId, entity, prjUserAssembler::toPrjUserPo,
                prjUserMapper::deleteByOrgIdAndId, prjUserMapper::insertOrUpdate);

        Set<PrjUserImportResult> prjUserImportResults = entity.getPrjUserImportResults();
        deleteConvertUpdateBatch(orgId, prjUserImportResults,
                prjUserAssembler::toPrjUserImportResultPos, prjUserImportResultMapper::deleteBatch,
                prjUserImportResultMapper::insertOrUpdateBatch);

        Set<PrjUserResult> prjUserResults = entity.getPrjUserResults();
        deleteConvertUpdateBatch(orgId, prjUserResults, prjUserAssembler::toPrjUserResultPos,
                prjUserResultMapper::deleteBatch, prjUserResultMapper::insertOrUpdateBatch);
    }

    /**
     * 批量保存
     *
     * @param entities
     */
    @Override
    public void save(@NonNull Collection<PrjUser> entities) {
        if (CollectionUtils.isEmpty(filterNull(entities))) {
            log.warn("LOG13405:");
            return;
        }

        String orgId = entities.iterator().next().getOrgId();

        deleteConvertUpdateBatch(orgId, entities, prjUserAssembler::toPrjUserPos,
                prjUserMapper::deleteBatch, prjUserMapper::insertOrUpdateBatch);

        Collection<PrjUserImportResult> prjUserImportResults =
                mapSubList(entities, PrjUser::getPrjUserImportResults);
        deleteConvertUpdateBatch(orgId, prjUserImportResults,
                prjUserAssembler::toPrjUserImportResultPos, prjUserImportResultMapper::deleteBatch,
                prjUserImportResultMapper::insertOrUpdateBatch);

        Collection<PrjUserResult> prjUserResults = mapSubList(entities, PrjUser::getPrjUserResults);
        deleteConvertUpdateBatch(orgId, prjUserResults, prjUserAssembler::toPrjUserResultPos,
                prjUserResultMapper::deleteBatch, prjUserResultMapper::insertOrUpdateBatch);
    }

    /**
     * 物理删除
     *
     * @param entity
     */
    @Override
    public void delete(@NonNull PrjUser entity) {
        prjUserMapper.deleteByOrgIdAndId(entity.getOrgId(), entity.getId());

        List<String> removedIds = getRemovePrjUserIds(entity);
        prjUserResultMapper.deleteBatch(entity.getOrgId(), removedIds);

        removedIds = getPrjUserImportResultIds(entity);
        prjUserImportResultMapper.deleteBatch(entity.getOrgId(), removedIds);
    }

    /**
     * 批量物理删除
     */
    @Override
    public void delete(@NonNull Collection<PrjUser> entities) {
        if (CollectionUtils.isEmpty(filterNull(entities))) {
            log.warn("LOG13395:");
            return;
        }

        String orgId = entities.iterator().next().getOrgId();

        List<String> prjUserIds = mapListThenFilterNull(entities, PrjUser::getId);
        prjUserMapper.deleteBatch(orgId, prjUserIds);

        Collection<String> prjUserResultIds =
                mapSubListSub(entities, PrjUser::getPrjUserResults, PrjUserResult::getId);
        prjUserResultMapper.deleteBatch(orgId, prjUserResultIds);

        var prjUserImportResultIds =
                mapSubListSub(entities, PrjUser::getPrjUserImportResults, PrjUserImportResult::getId);
        prjUserImportResultMapper.deleteBatch(orgId, prjUserImportResultIds);
    }

    private static List<String> getPrjUserImportResultIds(@Nonnull PrjUser entity) {
        Set<PrjUserImportResult> prjUserImportResults = entity.getPrjUserImportResults();
        return mapListThenFilterNull(prjUserImportResults, PrjUserImportResult::getId);
    }

    private static List<String> getRemovePrjUserIds(@Nonnull PrjUser entity) {
        Set<PrjUserResult> prjUserResults = entity.getPrjUserResults();
        return mapListThenFilterNull(prjUserResults, PrjUserResult::getId);
    }

    private PrjUser loadSub(@NonNull PrjUser prjUser, @NonNull PrjUser.LoadConfig loadConfig) {
        String orgId = prjUser.getOrgId();
        String prjId = prjUser.getPrjId();
        String userId = prjUser.getUserId();
        if (loadConfig.loadPrjUserResults()) {
            loadPrjUserResults(orgId, prjId, userId).ifPresent(prjUser::addPrjUserResults);
        }
        if (loadConfig.loadPrjUserImportResults()) {
            loadPrjUserImportResults(orgId, prjId, userId).ifPresent(
                    prjUser::addPrjUserImportResults);
        }
        return prjUser;
    }

    private Optional<Collection<PrjUserImportResult>> loadPrjUserImportResults(
            @NonNull String orgId, @NonNull String prjId, @NonNull String userId) {
        List<PrjUserImportResultPO> prjUserImportResultPos =
                prjUserImportResultMapper.selectByPrjIdAndUserId(orgId, prjId, userId);
        return Optional.ofNullable(prjUserAssembler.toPrjUserImportResults(prjUserImportResultPos));
    }

    private Optional<Collection<PrjUserResult>> loadPrjUserResults(
            @NonNull String orgId, @NonNull String prjId, @Nonnull String userId) {
        List<String> userIds = Collections.singletonList(userId);
        List<PrjUserResultPO> prjUserResultPos =
                prjUserResultMapper.listByPrjIdAndUserIds(orgId, prjId, userIds);
        return Optional.ofNullable(prjUserAssembler.toPrjUserResults(prjUserResultPos));
    }

}
