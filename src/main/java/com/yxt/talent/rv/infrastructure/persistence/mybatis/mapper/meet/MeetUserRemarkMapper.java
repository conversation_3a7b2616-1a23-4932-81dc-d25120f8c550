package com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.meet;

import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.meet.MeetUserRemarkPO;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MeetUserRemarkMapper {

    int updateById(@Nonnull @Param("updated") MeetUserRemarkPO updated);

    @Nonnull
    List<MeetUserRemarkPO> listByPrjIdAndMeetingIdAndUserId(
            @Nonnull @Param("orgId") String orgId, @Param("prjId") String prjId,
            @Nullable @Param("meetingId") String meetingId,
            @Nonnull @Param("userId") String userId);

    long insertOrUpdate(@Nonnull MeetUserRemarkPO calMeetUserRemark);
}
