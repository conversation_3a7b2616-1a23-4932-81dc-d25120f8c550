package com.yxt.talent.rv.infrastructure.persistence.mybatis.po.dmp.user;

import com.baomidou.mybatisplus.annotation.TableField;
import com.yxt.spsdk.democopy.DemoCopyConstants;
import com.yxt.spsdk.democopy.annotations.DemoCopyField;
import com.yxt.spsdk.democopy.bean.CopyFieldTypeEnum;
import com.yxt.talent.rv.infrastructure.common.constant.SprvDemoOrgCopyConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.annotation.Transient;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 人岗动态匹配项目-个人维度匹配结果
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class DmpUserDimResultPO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private String id;

    /**
     * 机构id
     */
    private String orgId;

    /**
     * 人岗动态匹配项目id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_DMP_ID, mappedNullStrategy = DemoCopyConstants.NULL_STRATEGY_EMPTY)
    private String dmpId;

    /**
     * 方案任务id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_DMP_TASK_ID, mappedNullStrategy = DemoCopyConstants.NULL_STRATEGY_EMPTY)
    private String taskId;

    /**
     * 学员id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = DemoCopyConstants.UDP_USER_ID, mappedNullStrategy = DemoCopyConstants.NULL_STRATEGY_EMPTY)
    private String userId;

    /**
     * 维度id，指向rv_dmp_task_dim.id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPRV_DMP_TASK_DIM_ID, mappedNullStrategy = DemoCopyConstants.NULL_STRATEGY_EMPTY)
    private String dimId;

    /**
     * 任职资格维度id
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPSD_JQ_DIM_ID)
    private String jqDimId;

    /**
     * 是否达标：0-不达标 1-达标 2-异常不达标（异常，可能因维度在任职资格中被删除找不到了）
     */
    private Integer matched = 0;

    /**
     * 创建人主键
     */
    private String createUserId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人主键
     */
    private String updateUserId;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 学员该维度的表单综合分，由测评提供
     */
    private BigDecimal formScore;

    /**
     * 学员该维度的表单综合分匹配到的能力等级，由测评提供
     */
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPSD_SKILL_LEVEL_ID)
    private String skillLevelId;

    /**
     * 维度异常原因，当matched=2时有值
     */
    private String exception;

    /**
     * DMP规则中设置的维度得分（根据达标、未达标、匹配能力层级之后获得的分）
     */
    private BigDecimal score;

    /**
     * 经过规则匹配转换之后的最终维度权重得分，即 score * rv_dmp_rule_score.weight
     */
    private BigDecimal weightScore;

    /**
     * 计算出结果的具体时间点（动态匹配任务的维度该值与create_time字段一致，表单任务的维度该值由测评方给出）
     */
    private LocalDateTime formClacTime;

    /**
     * 维度类型：1、指标 2、标签 3、普通文本 4、能力模型，5、任务模型，6、资格证书 7、知识点
     */
    @Transient
    @TableField(exist = false)
    private Integer dimType;

    /**
     * 能力模型id
     */
    @Transient
    @TableField(exist = false)
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPSD_SKILL_MODEL_ID)
    private String jqSkillModelId;

    /**
     * 任务模型id
     */
    @Transient
    @TableField(exist = false)
    @DemoCopyField(fieldType = CopyFieldTypeEnum.ID_MAP, idMapKey = SprvDemoOrgCopyConstants.SPSD_RT_MODEL_ID)
    private String jqRtModelId;

}