import re


def reorder_annotations_in_file(file_path):
    """在Java文件中重排序方法的注解，考虑多行注解问题"""
    try:
        with open(file_path, 'r+', encoding='utf-8') as file:
            content = file.read()

            # 定义一个正则表达式，匹配注解，包括跨多行的情况
            pattern = re.compile(r'((@\w+)(\([^)]*\)[\s\S]*?)?)?(public\s+\w+\s+\w+\(.*?\)\s*{)')

            # 使用finditer找到所有匹配的方法声明，包括其注解
            matches = list(pattern.finditer(content))
            annotations_blocks = []

            for match in matches:
                annotations_block = match.group(0)

                # 使用正则表达式找到所有单独的注解块
                annotation_pattern = re.compile(r'@\w+(\([^\)]*?\)[\s\S]*?)*?(?=(@|$))')
                annotations = annotation_pattern.findall(annotations_block)

                # 将注解和注解的长度作为元组存储
                annotations_with_length = [(a[0], len(a[0])) for a in annotations]

                # 根据注解长度重排序
                sorted_annotations = sorted(annotations_with_length, key=lambda x: x[1],
                                            reverse=True)
                sorted_annotations_str = ''.join([a[0] for a in sorted_annotations])

                # 替换原内容
                content = content.replace(annotations_block, sorted_annotations_str)

            # 移动文件指针到开头，覆盖写入更新后的内容
            file.seek(0)
            file.write(content)
            file.truncate()  # 删除原始文件剩余的内容

    except Exception as e:
        print(f"Error processing file {file_path}: {e}")


# 请注意，以下函数调用应替换为实际的文件路径，并在实际环境中运行。
reorder_annotations_in_file("/com/yxt/talent/rv/controller/manage/dmp/DmpManageController.java")
